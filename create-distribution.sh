#!/bin/bash

echo "Creating Database Backup & Restore Tool Distribution Package..."
echo

# Create timestamp for version
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
VERSION="v1.0_$TIMESTAMP"

# Create distribution archive
ARCHIVE_NAME="DatabaseBackupApp_$VERSION.zip"

echo "Creating archive: $ARCHIVE_NAME"

# Create the zip file
cd Distribution
zip -r "../$ARCHIVE_NAME" . -x "*.DS_Store" "*/.*"
cd ..

echo
echo "Distribution package created successfully!"
echo "File: $ARCHIVE_NAME"
echo "Size: $(du -h "$ARCHIVE_NAME" | cut -f1)"
echo
echo "Contents:"
echo "- Windows-x64/DatabaseBackupApp.exe + installer"
echo "- Linux-x64/DatabaseBackupApp + installer"  
echo "- macOS-x64/DatabaseBackupApp + installer"
echo "- README.md with installation instructions"
echo
echo "Ready for distribution! 🎉"
