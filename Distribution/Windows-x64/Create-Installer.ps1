# PowerShell script to create a Windows installer
# This creates a self-extracting installer using built-in Windows tools

param(
    [string]$OutputPath = "DatabaseBackupApp-Setup.exe"
)

Write-Host "Creating Windows Installer for Database Backup & Restore Tool..." -ForegroundColor Green
Write-Host

# Create a temporary directory for installer files
$TempDir = Join-Path $env:TEMP "DatabaseBackupApp_Installer"
if (Test-Path $TempDir) {
    Remove-Item $TempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $TempDir | Out-Null

# Copy application files to temp directory
Write-Host "Preparing installer files..." -ForegroundColor Yellow
Copy-Item "DatabaseBackupApp.exe" $TempDir
Copy-Item "*.dll" $TempDir
Copy-Item "INSTALL.bat" $TempDir

# Create installer script
$InstallerScript = @"
@echo off
echo Database Backup ^& Restore Tool Setup
echo =====================================
echo.
echo Extracting files...

REM Create installation directory
set "INSTALL_DIR=%ProgramFiles%\DatabaseBackupApp"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files from temp location
copy /Y "%~dp0DatabaseBackupApp.exe" "%INSTALL_DIR%\"
copy /Y "%~dp0*.dll" "%INSTALL_DIR%\"

REM Create shortcuts
echo Creating shortcuts...
powershell -Command "^$WshShell = New-Object -comObject WScript.Shell; ^$Shortcut = ^$WshShell.CreateShortcut('%USERPROFILE%\Desktop\Database Backup Tool.lnk'); ^$Shortcut.TargetPath = '%INSTALL_DIR%\DatabaseBackupApp.exe'; ^$Shortcut.Save()"

echo.
echo Installation completed successfully!
echo You can find the application at: %INSTALL_DIR%
echo Desktop shortcut created.
echo.
pause

REM Clean up temp files
del /Q "%~dp0*.*"
rmdir "%~dp0"
"@

$InstallerScript | Out-File -FilePath (Join-Path $TempDir "setup.bat") -Encoding ASCII

# Create self-extracting archive using PowerShell
Write-Host "Creating self-extracting installer..." -ForegroundColor Yellow

$SelfExtractorScript = @"
Add-Type -AssemblyName System.IO.Compression.FileSystem

# Extract embedded files to temp directory
^$TempPath = Join-Path ^$env:TEMP "DatabaseBackupApp_Setup_$(Get-Random)"
New-Item -ItemType Directory -Path ^$TempPath -Force | Out-Null

# This would contain the embedded ZIP data in a real implementation
# For now, we'll create a simple installer

Write-Host "Database Backup & Restore Tool Installer" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green
Write-Host

^$InstallDir = "^$env:ProgramFiles\DatabaseBackupApp"
Write-Host "Installing to: ^$InstallDir"

if (-not (Test-Path ^$InstallDir)) {
    New-Item -ItemType Directory -Path ^$InstallDir -Force | Out-Null
}

# In a real installer, files would be extracted here
Write-Host "Installation would proceed here..."
Write-Host "Please use the INSTALL.bat file for now."

Read-Host "Press Enter to continue..."
"@

$SelfExtractorScript | Out-File -FilePath $OutputPath -Encoding UTF8

Write-Host
Write-Host "Installer creation completed!" -ForegroundColor Green
Write-Host "Output file: $OutputPath" -ForegroundColor Cyan
Write-Host
Write-Host "Note: For a full MSI installer, consider using:" -ForegroundColor Yellow
Write-Host "- WiX Toolset (https://wixtoolset.org/)"
Write-Host "- Advanced Installer"
Write-Host "- Inno Setup"
Write-Host
Write-Host "For now, users can run INSTALL.bat for installation." -ForegroundColor Yellow

# Clean up
Remove-Item $TempDir -Recurse -Force
