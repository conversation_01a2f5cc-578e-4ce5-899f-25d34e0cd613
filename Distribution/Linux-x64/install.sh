#!/bin/bash

echo "========================================"
echo "Database Backup & Restore Tool Installer"
echo "========================================"
echo

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "Running as root..."
    INSTALL_DIR="/opt/DatabaseBackupApp"
    DESKTOP_FILE="/usr/share/applications/database-backup-tool.desktop"
    BIN_LINK="/usr/local/bin/database-backup-tool"
else
    echo "Running as regular user..."
    INSTALL_DIR="$HOME/.local/share/DatabaseBackupApp"
    DESKTOP_FILE="$HOME/.local/share/applications/database-backup-tool.desktop"
    BIN_LINK="$HOME/.local/bin/database-backup-tool"
    
    # Create local directories if they don't exist
    mkdir -p "$HOME/.local/share/applications"
    mkdir -p "$HOME/.local/bin"
fi

echo "Installing Database Backup & Restore Tool..."
echo "Installation directory: $INSTALL_DIR"
echo

# Create installation directory
mkdir -p "$INSTALL_DIR"
echo "Created installation directory."

# Copy files
echo "Copying application files..."
cp DatabaseBackupApp "$INSTALL_DIR/"
cp *.so "$INSTALL_DIR/" 2>/dev/null || true
cp *.dll "$INSTALL_DIR/" 2>/dev/null || true
cp createdump "$INSTALL_DIR/" 2>/dev/null || true

# Make executable
chmod +x "$INSTALL_DIR/DatabaseBackupApp"
echo "Application files copied successfully."
echo

# Create desktop entry
echo "Creating desktop entry..."
cat > "$DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Database Backup Tool
Comment=Database Backup and Restore Tool
Exec=$INSTALL_DIR/DatabaseBackupApp
Icon=database
Terminal=false
Categories=Development;Database;
StartupNotify=true
EOF

echo "Desktop entry created."

# Create symbolic link for command line access
echo "Creating command line shortcut..."
if [ -w "$(dirname "$BIN_LINK")" ]; then
    ln -sf "$INSTALL_DIR/DatabaseBackupApp" "$BIN_LINK"
    echo "Command line shortcut created: $BIN_LINK"
else
    echo "Cannot create command line shortcut (insufficient permissions)"
fi

echo
echo "========================================"
echo "Installation completed successfully!"
echo "========================================"
echo
echo "The application has been installed to: $INSTALL_DIR"
echo "Desktop entry created: $DESKTOP_FILE"
echo
echo "You can now run the application:"
echo "- From application menu (search for 'Database Backup Tool')"
echo "- Command line: $INSTALL_DIR/DatabaseBackupApp"
if [ -L "$BIN_LINK" ]; then
    echo "- Command line shortcut: database-backup-tool"
fi
echo
echo "Prerequisites for full functionality:"
echo "- mysql-client (for MySQL backups): sudo apt install mysql-client"
echo "- postgresql-client (for PostgreSQL backups): sudo apt install postgresql-client"
echo "- SQL Server tools (for SQL Server backups): Install mssql-tools"
echo "- CIFS utilities (for SMB shares): sudo apt install cifs-utils"
echo
echo "Installation complete!"
