{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {}, ".NETCoreApp,Version=v9.0/win-x64": {"DatabaseBackupApp/1.0.0": {"dependencies": {"Avalonia": "11.0.10", "Avalonia.Desktop": "11.0.10", "Avalonia.Fonts.Inter": "11.0.10", "Avalonia.Themes.Fluent": "11.0.10", "Microsoft.Data.SqlClient": "5.1.2", "Microsoft.NET.ILLink.Tasks": "9.0.7", "MySql.Data": "8.2.0", "Npgsql": "8.0.1", "System.IO.FileSystem.AccessControl": "5.0.0", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "9.0.7"}, "runtime": {"DatabaseBackupApp.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.7": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "14.0.725.31616"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "9.0.725.31616"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "0.0.0.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "0.0.0.0"}, "clretwrc.dll": {"fileVersion": "0.0.0.0"}, "clrgc.dll": {"fileVersion": "0.0.0.0"}, "clrgcexp.dll": {"fileVersion": "0.0.0.0"}, "clrjit.dll": {"fileVersion": "0.0.0.0"}, "coreclr.dll": {"fileVersion": "0.0.0.0"}, "createdump.exe": {"fileVersion": "0.0.0.0"}, "hostfxr.dll": {"fileVersion": "0.0.0.0"}, "hostpolicy.dll": {"fileVersion": "0.0.0.0"}, "mscordaccore.dll": {"fileVersion": "0.0.0.0"}, "mscordaccore_amd64_amd64_9.0.725.31616.dll": {"fileVersion": "0.0.0.0"}, "mscordbi.dll": {"fileVersion": "0.0.0.0"}, "mscorrc.dll": {"fileVersion": "0.0.0.0"}, "msquic.dll": {"fileVersion": "0.0.0.0"}}}, "Avalonia/11.0.10": {"dependencies": {"Avalonia.BuildServices": "0.0.29", "Avalonia.Remote.Protocol": "11.0.10", "MicroCom.Runtime": "0.11.0", "System.ComponentModel.Annotations": "4.5.0"}, "runtime": {"lib/net6.0/Avalonia.Base.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}, "lib/net6.0/Avalonia.Controls.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}, "lib/net6.0/Avalonia.DesignerSupport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/Avalonia.Dialogs.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}, "lib/net6.0/Avalonia.Markup.Xaml.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}, "lib/net6.0/Avalonia.Markup.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}, "lib/net6.0/Avalonia.Metal.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}, "lib/net6.0/Avalonia.MicroCom.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}, "lib/net6.0/Avalonia.OpenGL.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}, "lib/net6.0/Avalonia.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Avalonia.Angle.Windows.Natives/2.1.0.2023020321": {"native": {"runtimes/win-x64/native/av_libglesv2.dll": {"fileVersion": "0.0.0.0"}}}, "Avalonia.BuildServices/0.0.29": {}, "Avalonia.Desktop/11.0.10": {"dependencies": {"Avalonia": "11.0.10", "Avalonia.Native": "11.0.10", "Avalonia.Skia": "11.0.10", "Avalonia.Win32": "11.0.10", "Avalonia.X11": "11.0.10"}, "runtime": {"lib/net6.0/Avalonia.Desktop.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Avalonia.Fonts.Inter/11.0.10": {"dependencies": {"Avalonia": "11.0.10"}, "runtime": {"lib/net6.0/Avalonia.Fonts.Inter.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Avalonia.FreeDesktop/11.0.10": {"dependencies": {"Avalonia": "11.0.10", "Tmds.DBus.Protocol": "0.15.0"}, "runtime": {"lib/net6.0/Avalonia.FreeDesktop.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Avalonia.Native/11.0.10": {"dependencies": {"Avalonia": "11.0.10"}, "runtime": {"lib/net6.0/Avalonia.Native.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Avalonia.Remote.Protocol/11.0.10": {"runtime": {"lib/net6.0/Avalonia.Remote.Protocol.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Avalonia.Skia/11.0.10": {"dependencies": {"Avalonia": "11.0.10", "HarfBuzzSharp": "7.3.0", "HarfBuzzSharp.NativeAssets.Linux": "7.3.0", "HarfBuzzSharp.NativeAssets.WebAssembly": "7.3.0", "SkiaSharp": "2.88.7", "SkiaSharp.NativeAssets.Linux": "2.88.7", "SkiaSharp.NativeAssets.WebAssembly": "2.88.7"}, "runtime": {"lib/net6.0/Avalonia.Skia.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Avalonia.Themes.Fluent/11.0.10": {"dependencies": {"Avalonia": "11.0.10"}, "runtime": {"lib/net6.0/Avalonia.Themes.Fluent.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Avalonia.Win32/11.0.10": {"dependencies": {"Avalonia": "11.0.10", "Avalonia.Angle.Windows.Natives": "2.1.0.2023020321", "System.Numerics.Vectors": "4.5.0"}, "runtime": {"lib/net6.0/Avalonia.Win32.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Avalonia.X11/11.0.10": {"dependencies": {"Avalonia": "11.0.10", "Avalonia.FreeDesktop": "11.0.10", "Avalonia.Skia": "11.0.10"}, "runtime": {"lib/net6.0/Avalonia.X11.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Azure.Core/1.25.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "7.0.2", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net5.0/Azure.Core.dll": {"assemblyVersion": "1.25.0.0", "fileVersion": "1.2500.22.33004"}}}, "Azure.Identity/1.7.0": {"dependencies": {"Azure.Core": "1.25.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.Identity.Client.Extensions.Msal": "2.19.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "7.0.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.700.22.46903"}}}, "BouncyCastle.Cryptography/2.2.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.2.1.47552"}}}, "Google.Protobuf/3.21.9": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.21.9.0", "fileVersion": "3.21.9.0"}}}, "HarfBuzzSharp/7.3.0": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0"}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"assemblyVersion": "*******", "fileVersion": "7.3.0.0"}}}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0": {"dependencies": {"HarfBuzzSharp": "7.3.0"}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0": {}, "HarfBuzzSharp.NativeAssets.WebAssembly/7.3.0": {}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0": {"native": {"runtimes/win-x64/native/libHarfBuzzSharp.dll": {"fileVersion": "0.0.0.0"}}}, "K4os.Compression.LZ4/1.3.5": {"runtime": {"lib/net6.0/K4os.Compression.LZ4.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "K4os.Compression.LZ4.Streams/1.3.5": {"dependencies": {"K4os.Compression.LZ4": "1.3.5", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "6.0.3"}, "runtime": {"lib/net6.0/K4os.Compression.LZ4.Streams.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "K4os.Hash.xxHash/1.0.8": {"runtime": {"lib/net6.0/K4os.Hash.xxHash.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MicroCom.Runtime/0.11.0": {"runtime": {"lib/net5.0/MicroCom.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/5.1.2": {"dependencies": {"Azure.Identity": "1.7.0", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.1", "Microsoft.Identity.Client": "4.47.2", "Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.24.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "7.0.2", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.12.23290.5"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"native": {"runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"fileVersion": "0.0.0.0"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Identity.Client/4.47.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.47.2.0", "fileVersion": "4.47.2.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"dependencies": {"Microsoft.Identity.Client": "4.47.2", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.19.3.0", "fileVersion": "2.19.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.24.0", "System.Text.Encoding": "4.3.0", "System.Text.Json": "7.0.1"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Logging/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.24.0", "System.IdentityModel.Tokens.Jwt": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Tokens/6.24.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.24.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.NET.ILLink.Tasks/9.0.7": {}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "MySql.Data/8.2.0": {"dependencies": {"BouncyCastle.Cryptography": "2.2.1", "Google.Protobuf": "3.21.9", "K4os.Compression.LZ4.Streams": "1.3.5", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "7.0.2", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Runtime.Loader": "4.3.0", "System.Security.Permissions": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Json": "7.0.1", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.7.1"}, "runtime": {"lib/net8.0/MySql.Data.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.0.0"}}, "native": {"runtimes/win-x64/native/comerr64.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/gssapi64.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/k5sprt64.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/krb5_64.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/krbcc64.dll": {"fileVersion": "0.0.0.0"}}}, "Npgsql/8.0.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.0"}}}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "SkiaSharp/2.88.7": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.7", "SkiaSharp.NativeAssets.macOS": "2.88.7"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.7.0"}}}, "SkiaSharp.NativeAssets.Linux/2.88.7": {"dependencies": {"SkiaSharp": "2.88.7"}}, "SkiaSharp.NativeAssets.macOS/2.88.7": {}, "SkiaSharp.NativeAssets.WebAssembly/2.88.7": {}, "SkiaSharp.NativeAssets.Win32/2.88.7": {"native": {"runtimes/win-x64/native/libSkiaSharp.dll": {"fileVersion": "0.0.0.0"}}}, "System.Buffers/4.5.1": {}, "System.ComponentModel.Annotations/4.5.0": {}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Diagnostics.DiagnosticSource/7.0.2": {}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/5.0.0": {}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.IO.FileSystem.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.IO.Pipelines/6.0.3": {}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.1"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/7.0.0": {}, "System.Text.Json/7.0.1": {"dependencies": {"System.Text.Encodings.Web": "7.0.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Tmds.DBus.Protocol/0.15.0": {"dependencies": {"System.IO.Pipelines": "6.0.3"}, "runtime": {"lib/net6.0/Tmds.DBus.Protocol.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "ZstdSharp.Port/0.7.1": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"DatabaseBackupApp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.7": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Avalonia/11.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-EH1FyD1SA7G/TfLmb7JKQlZiOBqr6VzttJMtA5Hnc/c1623zJej0PRuQlqn8Ad6qWKorrKEypBGA9Gye3uWDrA==", "path": "avalonia/11.0.10", "hashPath": "avalonia.11.0.10.nupkg.sha512"}, "Avalonia.Angle.Windows.Natives/2.1.0.2023020321": {"type": "package", "serviceable": true, "sha512": "sha512-Zlkkb8ipxrxNWVPCJgMO19fpcpYPP+bpOQ+jPtCFj8v+TzVvPdnGHuyv9IMvSHhhMfEpps4m4hjaP4FORQYVAA==", "path": "avalonia.angle.windows.natives/2.1.0.2023020321", "hashPath": "avalonia.angle.windows.natives.2.1.0.2023020321.nupkg.sha512"}, "Avalonia.BuildServices/0.0.29": {"type": "package", "serviceable": true, "sha512": "sha512-U4eJLQdoDNHXtEba7MZUCwrBErBTxFp6sUewXBOdAhU0Kwzwaa/EKFcYm8kpcysjzKtfB4S0S9n0uxKZFz/ikw==", "path": "avalonia.buildservices/0.0.29", "hashPath": "avalonia.buildservices.0.0.29.nupkg.sha512"}, "Avalonia.Desktop/11.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-jPflQRB94sr3D7tgcUuSHnvK212ZtoM2oBZVQoP/musPWiu56LZ+o7+bAt8TMGhkUBMAylZ5u+tMGCe7EUwbEA==", "path": "avalonia.desktop/11.0.10", "hashPath": "avalonia.desktop.11.0.10.nupkg.sha512"}, "Avalonia.Fonts.Inter/11.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-4hVv6u5o8NxfPv2pl1Mg0VTYzxHkc85Wnye3sQJCQtbrd8s9v3DSEYK68zT4wBFlCtc4JHUP85PfyZuoTkf08A==", "path": "avalonia.fonts.inter/11.0.10", "hashPath": "avalonia.fonts.inter.11.0.10.nupkg.sha512"}, "Avalonia.FreeDesktop/11.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-QLVn1pjCe5ez4cH5B+NmhT61xK502mjxPxkswIbag5FB45tuNOF5zRszH+dN81rNw+VSf/J6PVXdmHz/FojyDw==", "path": "avalonia.freedesktop/11.0.10", "hashPath": "avalonia.freedesktop.11.0.10.nupkg.sha512"}, "Avalonia.Native/11.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-eJUPh4VOBomF36DzJQ+M+T1lVBVhpK3Ryx3xNnhVvYcW3dPNgugXApSbxulSipWnMfJh4DAKQD2Mt3ahy3Tp8Q==", "path": "avalonia.native/11.0.10", "hashPath": "avalonia.native.11.0.10.nupkg.sha512"}, "Avalonia.Remote.Protocol/11.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-4/zXSx7P2+4g7nIkSd8aIfEfRnvHjpeSEBjJznzE5iJBYO98B78wDPWEkyN8sQBJnLghaNKKsGCApx3g5R0Gsg==", "path": "avalonia.remote.protocol/11.0.10", "hashPath": "avalonia.remote.protocol.11.0.10.nupkg.sha512"}, "Avalonia.Skia/11.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-0rDySePqUkg6cMBZEXp+5P04XgyIXOcqhsCaWhrwFAqxcp5qKjILSCNvt0pden3TqCRjuNuGaWGV+qyk58l9vA==", "path": "avalonia.skia/11.0.10", "hashPath": "avalonia.skia.11.0.10.nupkg.sha512"}, "Avalonia.Themes.Fluent/11.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-V7xu7ddtUFwYHb+u3S4he2/PGGUHkeTI66j3PyGTZi0Uz8Ma4prb/Lgz7Qd/vpO4lukxw1G4DmxB1tClNS9qUw==", "path": "avalonia.themes.fluent/11.0.10", "hashPath": "avalonia.themes.fluent.11.0.10.nupkg.sha512"}, "Avalonia.Win32/11.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-XD4fiRpiLHB6Q8e9Vevj42rYQvdnrqs56Jwu9T4UaClcuk578fUH1LP/vl9q/n8LrEUyng01Q7vtpp5sgwsXDw==", "path": "avalonia.win32/11.0.10", "hashPath": "avalonia.win32.11.0.10.nupkg.sha512"}, "Avalonia.X11/11.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-WUhPBIYqjJdBf1TgUARYNyH1clntZ435eQzR+db4fxrg4OemePX6nJTp1Lh7G/hTVEvRgWCoYFccqm/6vsfJQQ==", "path": "avalonia.x11/11.0.10", "hashPath": "avalonia.x11.11.0.10.nupkg.sha512"}, "Azure.Core/1.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-X8Dd4sAggS84KScWIjEbFAdt2U1KDolQopTPoHVubG2y3CM54f9l6asVrP5Uy384NWXjsspPYaJgz5xHc+KvTA==", "path": "azure.core/1.25.0", "hashPath": "azure.core.1.25.0.nupkg.sha512"}, "Azure.Identity/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHEiCO/8+MfNc9nH5dVew/+FvxdaGrkRL4OMNwIz0W79+wtJyEoeRlXJ3SrXhoy9XR58geBYKmzMR83VO7bcAw==", "path": "azure.identity/1.7.0", "hashPath": "azure.identity.1.7.0.nupkg.sha512"}, "BouncyCastle.Cryptography/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-A6Zr52zVqJKt18ZBsTnX0qhG0kwIQftVAjLmszmkiR/trSp8H+xj1gUOzk7XHwaKgyREMSV1v9XaKrBUeIOdvQ==", "path": "bouncycastle.cryptography/2.2.1", "hashPath": "bouncycastle.cryptography.2.2.1.nupkg.sha512"}, "Google.Protobuf/3.21.9": {"type": "package", "serviceable": true, "sha512": "sha512-OTpFujTgkmqMLbg3KT7F/iuKi1rg6s5FCS2M9XcVLDn40zL8wgXm37CY/F6MeOEXKjdcnXGCN/h7oyMkVydVsg==", "path": "google.protobuf/3.21.9", "hashPath": "google.protobuf.3.21.9.nupkg.sha512"}, "HarfBuzzSharp/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OrQLaxtZMIeS2yHSUtsKzeSdk9CPaCpyJ/JCs+wLfRGatjE8MLUS6LGj6vdbGRxqRavcXs79C9O3oWe6FJR0JQ==", "path": "harfbuzzsharp/7.3.0", "hashPath": "harfbuzzsharp.7.3.0.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6F2pEBTN0zTRgQ3caJQRGQkZZizZwHHCbu+rTv+gvwteNBOpqOLD5GE4dB9TFjNNpnyHXtfuMD86JuUra9UvA==", "path": "harfbuzzsharp.nativeassets.linux/7.3.0", "hashPath": "harfbuzzsharp.nativeassets.linux.7.3.0.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LWcFJ39j+dN0KK8c/GJJZPPZPL9TqT2FA42/LRGqzUMmSm5LYbINOMnPvUr7RuLR6RFSmKIrgrlgObR8G5ho2A==", "path": "harfbuzzsharp.nativeassets.macos/7.3.0", "hashPath": "harfbuzzsharp.nativeassets.macos.7.3.0.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.WebAssembly/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cnl4I6P+VeujfSSD3ZrC5f0TrTGt9EKgCOoZ3LpgLI2xobBKLi5bxOaN2oY6B0xVXxQEhEaWBotg7AuECg00Iw==", "path": "harfbuzzsharp.nativeassets.webassembly/7.3.0", "hashPath": "harfbuzzsharp.nativeassets.webassembly.7.3.0.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ulEewLMk+dNmbmpy15ny/YusI6JNUWqchF080TV2jgfFBXPXjWm767JleDi/S7hp8eDeEN6GYIIxpvNr5fLvIw==", "path": "harfbuzzsharp.nativeassets.win32/7.3.0", "hashPath": "harfbuzzsharp.nativeassets.win32.7.3.0.nupkg.sha512"}, "K4os.Compression.LZ4/1.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-TS4mqlT0X1OlnvOGNfl02QdVUhuqgWuCnn7UxupIa7C9Pb6qlQ5yZA2sPhRh0OSmVULaQU64KV4wJuu//UyVQQ==", "path": "k4os.compression.lz4/1.3.5", "hashPath": "k4os.compression.lz4.1.3.5.nupkg.sha512"}, "K4os.Compression.LZ4.Streams/1.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-M0<PERSON>ufZI8ym3mm6F6HMSPz1jw7TJGdY74fjAtbIXATmnAva/8xLz50eQZJI9tf9mMeHUaFDg76N1BmEh8GR5zeA==", "path": "k4os.compression.lz4.streams/1.3.5", "hashPath": "k4os.compression.lz4.streams.1.3.5.nupkg.sha512"}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Wp2F7BamQ2Q/7Hk834nV9vRQapgcr8kgv9Jvfm8J3D0IhDqZMMl+a2yxUq5ltJitvXvQfB8W6K4F4fCbw/P6YQ==", "path": "k4os.hash.xxhash/1.0.8", "hashPath": "k4os.hash.xxhash.1.0.8.nupkg.sha512"}, "MicroCom.Runtime/0.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "path": "microcom.runtime/0.11.0", "hashPath": "microcom.runtime.0.11.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-q/F1HTOn9QLwgRp4esJIA1b2X15faeV8WozkNhvU3Zk0DcYDWUsEf5WMkbypY4CaNkZntOduods5wLyv8I699w==", "path": "microsoft.data.sqlclient/5.1.2", "hashPath": "microsoft.data.sqlclient.5.1.2.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-wNGM5ZTQCa2blc9ikXQouybGiyMd6IHPVJvAlBEPtr6JepZEOYeDxGyprYvFVeOxlCXs7avridZQ0nYkHzQWCQ==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.47.2": {"type": "package", "serviceable": true, "sha512": "sha512-SPgesZRbXoDxg8Vv7k5Ou0ee7uupVw0E8ZCc4GKw25HANRLz1d5OSr0fvTVQRnEswo5Obk8qD4LOapYB+n5kzQ==", "path": "microsoft.identity.client/4.47.2", "hashPath": "microsoft.identity.client.4.47.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"type": "package", "serviceable": true, "sha512": "sha512-zVVZjn8aW7W79rC1crioDgdOwaFTQorsSO6RgVlDDjc7MvbEGz071wSNrjVhzR0CdQn6Sefx7Abf1o7vasmrLg==", "path": "microsoft.identity.client.extensions.msal/2.19.3", "hashPath": "microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-X6aBK56Ot15qKyG7X37KsPnrwah+Ka55NJWPppWVTDi8xWq7CJgeNw2XyaeHgE1o/mW4THwoabZkBbeG2TPBiw==", "path": "microsoft.identitymodel.abstractions/6.24.0", "hashPath": "microsoft.identitymodel.abstractions.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-XDWrkThcxfuWp79AvAtg5f+uRS1BxkIbJnsG/e8VPzOWkYYuDg33emLjp5EWcwXYYIDsHnVZD/00kM/PYFQc/g==", "path": "microsoft.identitymodel.jsonwebtokens/6.24.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-qLYWDOowM/zghmYKXw1yfYKlHOdS41i8t4hVXr9bSI90zHqhyhQh9GwVy8pENzs5wHeytU23DymluC9NtgYv7w==", "path": "microsoft.identitymodel.logging/6.24.0", "hashPath": "microsoft.identitymodel.logging.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-+NzKCkvsQ8X1r/Ff74V7CFr9OsdMRaB6DsV+qpH7NNLdYJ8O4qHbmTnNEsjFcDmk/gVNDwhoL2gN5pkPVq0lwQ==", "path": "microsoft.identitymodel.protocols/6.24.0", "hashPath": "microsoft.identitymodel.protocols.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-a/2RRrc8C9qaw8qdD9hv1ES9YKFgxaqr/SnwMSLbwQZJSUQDd4qx1K4EYgWaQWs73R+VXLyKSxN0f/uE9CsBiQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.24.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZPqHi86UYuqJXJ7bLnlEctHKkPKT4lGUFbotoCNiXNCSL02emYlcxzGYsRGWWmbFEcYDMi2dcTLLYNzHqWOTsw==", "path": "microsoft.identitymodel.tokens/6.24.0", "hashPath": "microsoft.identitymodel.tokens.6.24.0.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-SZ1brSGoLnhLbE8QUZrtN6YwzN2gDT1wbx9qDBEfFFJcstiDTjJ6ygNuTPBV/K7SjGfx2YNbcJi5+ygbPOZpDg==", "path": "microsoft.net.illink.tasks/9.0.7", "hashPath": "microsoft.net.illink.tasks.9.0.7.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MySql.Data/8.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-p62lWlKvJJ5UnBNvtal3JM9SOyQaaIA4C6E5LO2TOlO8CF5BDX0I8HsihbzP8wOELZhD4I3vpferDRv7uBNkOg==", "path": "mysql.data/8.2.0", "hashPath": "mysql.data.8.2.0.nupkg.sha512"}, "Npgsql/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/XQX6n2gKQPnRoP5AhOnz33qL5V6CEY2vqluHPIvK4VZqY/sE/dHOFj+/8Ok7Y5Cbkj6/OZOFLZCiaKcryhS9A==", "path": "npgsql/8.0.1", "hashPath": "npgsql.8.0.1.nupkg.sha512"}, "runtime.any.System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "path": "runtime.any.system.io/4.3.0", "hashPath": "runtime.any.system.io.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "path": "runtime.any.system.reflection/4.3.0", "hashPath": "runtime.any.system.reflection.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "path": "runtime.any.system.reflection.primitives/4.3.0", "hashPath": "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "path": "runtime.any.system.runtime/4.3.0", "hashPath": "runtime.any.system.runtime.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "path": "runtime.any.system.text.encoding/4.3.0", "hashPath": "runtime.any.system.text.encoding.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "path": "runtime.any.system.threading.tasks/4.3.0", "hashPath": "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512"}, "SkiaSharp/2.88.7": {"type": "package", "serviceable": true, "sha512": "sha512-LJHAMrbWO00J7jXWLWehyjqFo29T4VzABimfJb4nICqpEe3c/KvQGWL4ItON8ymzhxYOeFgyxeRzuNzO4GHSug==", "path": "skiasharp/2.88.7", "hashPath": "skiasharp.2.88.7.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/2.88.7": {"type": "package", "serviceable": true, "sha512": "sha512-i9VitS7/5D8Te3B1Gu7F6kakW9PYVnI3YC6MoR6NidreD9hDl1EIOQEBaa0eBsOsWNX5Bz92OVf6+7KbDrJvyg==", "path": "skiasharp.nativeassets.linux/2.88.7", "hashPath": "skiasharp.nativeassets.linux.2.88.7.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.7": {"type": "package", "serviceable": true, "sha512": "sha512-3jNzco4VjcYPFNxR9aNWcgweFXbTSdM1VpNRzCS4X0i1A1OuNqcaulrAvmntNpujeWxHo9e6WGh6FN8Jf5+XhA==", "path": "skiasharp.nativeassets.macos/2.88.7", "hashPath": "skiasharp.nativeassets.macos.2.88.7.nupkg.sha512"}, "SkiaSharp.NativeAssets.WebAssembly/2.88.7": {"type": "package", "serviceable": true, "sha512": "sha512-elmOOQTO0QXmnnHx7GliF7VNJqZkWgPqqPsXapEN0EEZJ9fGblYWmD6cqxTwaRTMCUFeLpn8+gTzY8j000MxZQ==", "path": "skiasharp.nativeassets.webassembly/2.88.7", "hashPath": "skiasharp.nativeassets.webassembly.2.88.7.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.7": {"type": "package", "serviceable": true, "sha512": "sha512-BCXmWdQ0oVck9vRwC8U3ocSaTHEx28VB+6qw9OxGIMQ86iO5bp4Flqk3IXH0l9Pbr7vWAUOpI212iaL9mTaUZQ==", "path": "skiasharp.nativeassets.win32/2.88.7", "hashPath": "skiasharp.nativeassets.win32.2.88.7.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg==", "path": "system.componentmodel.annotations/4.5.0", "hashPath": "system.componentmodel.annotations.4.5.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hYr3I9N9811e0Bjf2WNwAGGyTuAFbbTgX1RPLt/3Wbm68x3IGcX5Cl75CMmgT6WlNwLQ2tCCWfqYPpypjaf2xA==", "path": "system.diagnostics.diagnosticsource/7.0.2", "hashPath": "system.diagnostics.diagnosticsource.7.0.2.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qibsj9MPWq8S/C0FgvmsLfIlHLE7ay0MJIaAmK94ivN3VyDdglqReed5qMvdQhSL0BzK6v0Z1wB/sD88zVu6Jw==", "path": "system.identitymodel.tokens.jwt/6.24.0", "hashPath": "system.identitymodel.tokens.jwt.6.24.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "path": "system.io.filesystem.accesscontrol/5.0.0", "hashPath": "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "path": "system.private.uri/4.3.0", "hashPath": "system.private.uri.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtDEmCCiNl8JAduFKZ/r0Sw6XZNHwIicUYy/mXgMDGeOsZLshH37qi3oPRzFYiryVktiMoQLByMGPtRCEMYbeQ==", "path": "system.text.json/7.0.1", "hashPath": "system.text.json.7.0.1.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "Tmds.DBus.Protocol/0.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-QVo/Y39nTYcCKBqrZuwHjXdwaky0yTQPIT3qUTEEK2MZfDtZWrJ2XyZ59zH8LBgB2fL5cWaTuP2pBTpGz/GeDQ==", "path": "tmds.dbus.protocol/0.15.0", "hashPath": "tmds.dbus.protocol.0.15.0.nupkg.sha512"}, "ZstdSharp.Port/0.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-Idgg+mJEyAujqDPzA3APy9dNoyw0YQcNA65GgYjktDRtJ+nvx/hv+J+m6Eax3JJMGEYGy04oc5YNP6ZvQ3Y1vQ==", "path": "zstdsharp.port/0.7.1", "hashPath": "zstdsharp.port.0.7.1.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}