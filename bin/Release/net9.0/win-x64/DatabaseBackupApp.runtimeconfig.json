{"runtimeOptions": {"tfm": "net9.0", "includedFrameworks": [{"name": "Microsoft.NETCore.App", "version": "9.0.7"}], "configProperties": {"Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability": true, "System.ComponentModel.DefaultValueAttribute.IsSupported": false, "System.ComponentModel.Design.IDesignerHost.IsSupported": false, "System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization": false, "System.ComponentModel.TypeDescriptor.IsComObjectDescriptorSupported": false, "System.Reflection.Metadata.MetadataUpdater.IsSupported": false, "System.Resources.ResourceManager.AllowCustomResourceTypes": false, "System.Resources.UseSystemResourceKeys": false, "System.Runtime.CompilerServices.RuntimeFeature.IsDynamicCodeSupported": true, "System.Runtime.InteropServices.BuiltInComInterop.IsSupported": true, "System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting": false, "System.Runtime.InteropServices.EnableCppCLIHostActivation": false, "System.Runtime.InteropServices.Marshalling.EnableGeneratedComInterfaceComImportInterop": false, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "System.StartupHookProvider.IsSupported": false, "System.Text.Encoding.EnableUnsafeUTF7Encoding": false, "System.Text.Json.JsonSerializer.IsReflectionEnabledByDefault": false, "System.Threading.Thread.EnableAutoreleasePool": false}}}