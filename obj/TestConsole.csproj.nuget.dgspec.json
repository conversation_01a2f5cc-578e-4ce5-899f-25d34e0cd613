{"format": 1, "restore": {"/Users/<USER>/Documents/Test Projects/Desktop App - Database Backups/TestConsole.csproj": {}}, "projects": {"/Users/<USER>/Documents/Test Projects/Desktop App - Database Backups/TestConsole.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Test Projects/Desktop App - Database Backups/TestConsole.csproj", "projectName": "TestConsole", "projectPath": "/Users/<USER>/Documents/Test Projects/Desktop App - Database Backups/TestConsole.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Test Projects/Desktop App - Database Backups/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.2, )"}, "MySql.Data": {"target": "Package", "version": "[8.2.0, )"}, "Npgsql": {"target": "Package", "version": "[8.0.1, )"}, "System.IO.FileSystem.AccessControl": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.303/PortableRuntimeIdentifierGraph.json"}}}}}