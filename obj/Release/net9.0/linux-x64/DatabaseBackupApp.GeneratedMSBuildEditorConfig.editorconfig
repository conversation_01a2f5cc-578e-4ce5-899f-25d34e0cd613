is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = true
build_property.EnableTrimAnalyzer = true
build_property.IncludeAllContentForSelfExtract = 
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = DatabaseBackupApp
build_property.ProjectDir = /Users/<USER>/Documents/Test Projects/Desktop App/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = false
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[/Users/<USER>/Documents/Test Projects/Desktop App/App.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[/Users/<USER>/Documents/Test Projects/Desktop App/Views/MainWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
