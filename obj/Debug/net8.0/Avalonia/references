/Users/<USER>/.nuget/packages/avalonia/11.0.10/ref/net6.0/Avalonia.Base.dll
/Users/<USER>/.nuget/packages/avalonia.controls.colorpicker/11.0.10/lib/net6.0/Avalonia.Controls.ColorPicker.dll
/Users/<USER>/.nuget/packages/avalonia.controls.datagrid/11.0.10/lib/net6.0/Avalonia.Controls.DataGrid.dll
/Users/<USER>/.nuget/packages/avalonia/11.0.10/ref/net6.0/Avalonia.Controls.dll
/Users/<USER>/.nuget/packages/avalonia/11.0.10/ref/net6.0/Avalonia.DesignerSupport.dll
/Users/<USER>/.nuget/packages/avalonia.desktop/11.0.10/lib/net6.0/Avalonia.Desktop.dll
/Users/<USER>/.nuget/packages/avalonia.diagnostics/11.0.10/lib/net6.0/Avalonia.Diagnostics.dll
/Users/<USER>/.nuget/packages/avalonia/11.0.10/ref/net6.0/Avalonia.Dialogs.dll
/Users/<USER>/.nuget/packages/avalonia/11.0.10/ref/net6.0/Avalonia.dll
/Users/<USER>/.nuget/packages/avalonia.fonts.inter/11.0.10/lib/net6.0/Avalonia.Fonts.Inter.dll
/Users/<USER>/.nuget/packages/avalonia.freedesktop/11.0.10/lib/net6.0/Avalonia.FreeDesktop.dll
/Users/<USER>/.nuget/packages/avalonia/11.0.10/ref/net6.0/Avalonia.Markup.dll
/Users/<USER>/.nuget/packages/avalonia/11.0.10/ref/net6.0/Avalonia.Markup.Xaml.dll
/Users/<USER>/.nuget/packages/avalonia/11.0.10/ref/net6.0/Avalonia.Metal.dll
/Users/<USER>/.nuget/packages/avalonia/11.0.10/ref/net6.0/Avalonia.MicroCom.dll
/Users/<USER>/.nuget/packages/avalonia.native/11.0.10/lib/net6.0/Avalonia.Native.dll
/Users/<USER>/.nuget/packages/avalonia/11.0.10/ref/net6.0/Avalonia.OpenGL.dll
/Users/<USER>/.nuget/packages/avalonia.remote.protocol/11.0.10/lib/net6.0/Avalonia.Remote.Protocol.dll
/Users/<USER>/.nuget/packages/avalonia.skia/11.0.10/lib/net6.0/Avalonia.Skia.dll
/Users/<USER>/.nuget/packages/avalonia.themes.fluent/11.0.10/lib/net6.0/Avalonia.Themes.Fluent.dll
/Users/<USER>/.nuget/packages/avalonia.themes.simple/11.0.10/lib/net6.0/Avalonia.Themes.Simple.dll
/Users/<USER>/.nuget/packages/avalonia.win32/11.0.10/lib/net6.0/Avalonia.Win32.dll
/Users/<USER>/.nuget/packages/avalonia.x11/11.0.10/lib/net6.0/Avalonia.X11.dll
/Users/<USER>/.nuget/packages/azure.core/1.25.0/lib/net5.0/Azure.Core.dll
/Users/<USER>/.nuget/packages/azure.identity/1.7.0/lib/netstandard2.0/Azure.Identity.dll
/Users/<USER>/.nuget/packages/bouncycastle.cryptography/2.2.1/lib/net6.0/BouncyCastle.Cryptography.dll
/Users/<USER>/.nuget/packages/google.protobuf/3.21.9/lib/net5.0/Google.Protobuf.dll
/Users/<USER>/.nuget/packages/harfbuzzsharp/7.3.0/lib/net6.0/HarfBuzzSharp.dll
/Users/<USER>/.nuget/packages/k4os.compression.lz4/1.3.5/lib/net6.0/K4os.Compression.LZ4.dll
/Users/<USER>/.nuget/packages/k4os.compression.lz4.streams/1.3.5/lib/net6.0/K4os.Compression.LZ4.Streams.dll
/Users/<USER>/.nuget/packages/k4os.hash.xxhash/1.0.8/lib/net6.0/K4os.Hash.xxHash.dll
/Users/<USER>/.nuget/packages/microcom.runtime/0.11.0/lib/net5.0/MicroCom.Runtime.dll
/Users/<USER>/.nuget/packages/microsoft.bcl.asyncinterfaces/1.1.1/ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll
/Users/<USER>/.nuget/packages/microsoft.codeanalysis.csharp/3.8.0/lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll
/Users/<USER>/.nuget/packages/microsoft.codeanalysis.csharp.scripting/3.8.0/lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Scripting.dll
/Users/<USER>/.nuget/packages/microsoft.codeanalysis.common/3.8.0/lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll
/Users/<USER>/.nuget/packages/microsoft.codeanalysis.scripting.common/3.8.0/lib/netcoreapp3.1/Microsoft.CodeAnalysis.Scripting.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/Microsoft.CSharp.dll
/Users/<USER>/.nuget/packages/microsoft.data.sqlclient/5.1.2/ref/net6.0/Microsoft.Data.SqlClient.dll
/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.0/lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.0/lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll
/Users/<USER>/.nuget/packages/microsoft.identity.client/4.47.2/lib/netcoreapp2.1/Microsoft.Identity.Client.dll
/Users/<USER>/.nuget/packages/microsoft.identity.client.extensions.msal/2.19.3/lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll
/Users/<USER>/.nuget/packages/microsoft.identitymodel.abstractions/6.24.0/lib/net6.0/Microsoft.IdentityModel.Abstractions.dll
/Users/<USER>/.nuget/packages/microsoft.identitymodel.jsonwebtokens/6.24.0/lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll
/Users/<USER>/.nuget/packages/microsoft.identitymodel.logging/6.24.0/lib/net6.0/Microsoft.IdentityModel.Logging.dll
/Users/<USER>/.nuget/packages/microsoft.identitymodel.protocols/6.24.0/lib/net6.0/Microsoft.IdentityModel.Protocols.dll
/Users/<USER>/.nuget/packages/microsoft.identitymodel.protocols.openidconnect/6.24.0/lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
/Users/<USER>/.nuget/packages/microsoft.identitymodel.tokens/6.24.0/lib/net6.0/Microsoft.IdentityModel.Tokens.dll
/Users/<USER>/.nuget/packages/microsoft.sqlserver.server/1.0.0/lib/netstandard2.0/Microsoft.SqlServer.Server.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/Microsoft.VisualBasic.Core.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/Microsoft.VisualBasic.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/Microsoft.Win32.Primitives.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/Microsoft.Win32.Registry.dll
/Users/<USER>/.nuget/packages/microsoft.win32.systemevents/6.0.0/lib/net6.0/Microsoft.Win32.SystemEvents.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/mscorlib.dll
/Users/<USER>/.nuget/packages/mysql.data/8.2.0/lib/net8.0/MySql.Data.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/netstandard.dll
/Users/<USER>/.nuget/packages/npgsql/8.0.1/lib/net8.0/Npgsql.dll
/Users/<USER>/.nuget/packages/skiasharp/2.88.7/lib/net6.0/SkiaSharp.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.AppContext.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Buffers.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Collections.Concurrent.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Collections.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Collections.Immutable.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Collections.NonGeneric.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Collections.Specialized.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.ComponentModel.Annotations.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.ComponentModel.DataAnnotations.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.ComponentModel.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.ComponentModel.EventBasedAsync.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.ComponentModel.Primitives.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.ComponentModel.TypeConverter.dll
/Users/<USER>/.nuget/packages/system.configuration.configurationmanager/6.0.1/lib/net6.0/System.Configuration.ConfigurationManager.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Configuration.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Console.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Core.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Data.Common.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Data.DataSetExtensions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Data.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Diagnostics.Contracts.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Diagnostics.Debug.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Diagnostics.DiagnosticSource.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Diagnostics.FileVersionInfo.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Diagnostics.Process.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Diagnostics.StackTrace.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Diagnostics.TextWriterTraceListener.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Diagnostics.Tools.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Diagnostics.TraceSource.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Diagnostics.Tracing.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.dll
/Users/<USER>/.nuget/packages/system.drawing.common/6.0.0/lib/net6.0/System.Drawing.Common.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Drawing.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Drawing.Primitives.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Dynamic.Runtime.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Formats.Asn1.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Formats.Tar.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Globalization.Calendars.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Globalization.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Globalization.Extensions.dll
/Users/<USER>/.nuget/packages/system.identitymodel.tokens.jwt/6.24.0/lib/net6.0/System.IdentityModel.Tokens.Jwt.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.Compression.Brotli.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.Compression.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.Compression.FileSystem.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.Compression.ZipFile.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.FileSystem.AccessControl.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.FileSystem.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.FileSystem.DriveInfo.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.FileSystem.Primitives.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.FileSystem.Watcher.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.IsolatedStorage.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.MemoryMappedFiles.dll
/Users/<USER>/.nuget/packages/system.io.pipelines/6.0.3/lib/net6.0/System.IO.Pipelines.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.Pipes.AccessControl.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.Pipes.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.IO.UnmanagedMemoryStream.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Linq.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Linq.Expressions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Linq.Parallel.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Linq.Queryable.dll
/Users/<USER>/.nuget/packages/system.memory.data/1.0.2/lib/netstandard2.0/System.Memory.Data.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Memory.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.Http.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.Http.Json.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.HttpListener.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.Mail.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.NameResolution.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.NetworkInformation.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.Ping.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.Primitives.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.Quic.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.Requests.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.Security.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.ServicePoint.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.Sockets.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.WebClient.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.WebHeaderCollection.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.WebProxy.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.WebSockets.Client.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Net.WebSockets.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Numerics.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Numerics.Vectors.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.ObjectModel.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Reflection.DispatchProxy.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Reflection.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Reflection.Emit.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Reflection.Emit.ILGeneration.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Reflection.Emit.Lightweight.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Reflection.Extensions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Reflection.Metadata.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Reflection.Primitives.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Reflection.TypeExtensions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Resources.Reader.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Resources.ResourceManager.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Resources.Writer.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.CompilerServices.Unsafe.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.CompilerServices.VisualC.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.Extensions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.Handles.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.InteropServices.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.InteropServices.JavaScript.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.InteropServices.RuntimeInformation.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.Intrinsics.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.Loader.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.Numerics.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.Serialization.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.Serialization.Formatters.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.Serialization.Json.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.Serialization.Primitives.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Runtime.Serialization.Xml.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.AccessControl.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Claims.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Cryptography.Algorithms.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Cryptography.Cng.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Cryptography.Csp.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Cryptography.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Cryptography.Encoding.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Cryptography.OpenSsl.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Cryptography.Primitives.dll
/Users/<USER>/.nuget/packages/system.security.cryptography.protecteddata/6.0.0/lib/net6.0/System.Security.Cryptography.ProtectedData.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Cryptography.X509Certificates.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.dll
/Users/<USER>/.nuget/packages/system.security.permissions/6.0.0/lib/net6.0/System.Security.Permissions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Principal.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.Principal.Windows.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Security.SecureString.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.ServiceModel.Web.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.ServiceProcess.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Text.Encoding.CodePages.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Text.Encoding.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Text.Encoding.Extensions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Text.Json.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Text.RegularExpressions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Threading.Channels.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Threading.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Threading.Overlapped.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Threading.Tasks.Dataflow.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Threading.Tasks.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Threading.Tasks.Extensions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Threading.Tasks.Parallel.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Threading.Thread.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Threading.ThreadPool.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Threading.Timer.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Transactions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Transactions.Local.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.ValueTuple.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Web.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Web.HttpUtility.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Windows.dll
/Users/<USER>/.nuget/packages/system.windows.extensions/6.0.0/lib/net6.0/System.Windows.Extensions.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Xml.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Xml.Linq.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Xml.ReaderWriter.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Xml.Serialization.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Xml.XDocument.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Xml.XmlDocument.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Xml.XmlSerializer.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Xml.XPath.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/System.Xml.XPath.XDocument.dll
/Users/<USER>/.nuget/packages/tmds.dbus.protocol/0.15.0/lib/net6.0/Tmds.DBus.Protocol.dll
/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/ref/net8.0/WindowsBase.dll
/Users/<USER>/.nuget/packages/zstdsharp.port/0.7.1/lib/net7.0/ZstdSharp.dll
