<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.2" />
    <PackageReference Include="MySql.Data" Version="8.2.0" />
    <PackageReference Include="Npgsql" Version="8.0.1" />
    <PackageReference Include="System.IO.FileSystem.AccessControl" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="Models/DatabaseConnection.cs" />
    <Compile Include="Models/Profile.cs" />
    <Compile Include="Services/ProfileService.cs" />
    <Compile Include="TestConsole.cs" />
  </ItemGroup>

</Project>
