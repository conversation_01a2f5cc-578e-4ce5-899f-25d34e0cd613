# Database Backup & Restore Tool - Distribution Package

## 📦 **What's Included**

This distribution package contains self-contained executables for multiple platforms:

- **Windows-x64/**: Windows 64-bit executable and installer
- **Linux-x64/**: Linux 64-bit executable and installer  
- **macOS-x64/**: macOS 64-bit executable and installer

## 🚀 **Installation Instructions**

### **Windows Installation**
1. Navigate to the `Windows-x64` folder
2. **Right-click** on `INSTALL.bat` and select **"Run as administrator"**
3. Follow the installation prompts
4. The application will be installed to `C:\Program Files\DatabaseBackupApp`
5. Desktop and Start Menu shortcuts will be created

### **Linux Installation**
1. Navigate to the `Linux-x64` folder
2. Open terminal in this directory
3. Run: `sudo ./install.sh` (for system-wide installation)
   - Or: `./install.sh` (for user-only installation)
4. The application will be installed and a desktop entry created
5. You can run it from the application menu or command line

### **macOS Installation**
1. Navigate to the `macOS-x64` folder
2. Open terminal in this directory
3. Run: `sudo ./install.sh`
4. The application will be installed to `/Applications/DatabaseBackupApp`
5. An app bundle will be created for easy access

## 🎯 **Quick Start (No Installation)**

If you prefer not to install, you can run the application directly:

### **Windows**
- Double-click `Windows-x64/DatabaseBackupApp.exe`

### **Linux**
- Open terminal in `Linux-x64` folder
- Run: `./DatabaseBackupApp`

### **macOS**
- Open terminal in `macOS-x64` folder  
- Run: `./DatabaseBackupApp`

## 🔧 **Prerequisites**

The executables are **self-contained** and don't require .NET to be installed. However, for full database functionality, you'll need:

### **Database Client Tools**

#### **SQL Server**
- **Windows**: SQL Server Management Studio or sqlcmd
- **Linux/macOS**: mssql-tools package

#### **MySQL/MariaDB**
- **Windows**: MySQL Command Line Client
- **Linux**: `sudo apt install mysql-client` (Ubuntu/Debian)
- **macOS**: `brew install mysql-client`

#### **PostgreSQL**
- **Windows**: PostgreSQL client tools
- **Linux**: `sudo apt install postgresql-client` (Ubuntu/Debian)
- **macOS**: `brew install postgresql`

### **SMB/Network Share Support**
- **Windows**: Built-in support
- **Linux**: `sudo apt install cifs-utils` (Ubuntu/Debian)
- **macOS**: Built-in support

## 📋 **Application Features**

- ✅ **Cross-platform** desktop application
- ✅ **Multi-database support**: SQL Server, MySQL/MariaDB, PostgreSQL
- ✅ **Backup operations** to local directories or SMB network shares
- ✅ **Restore operations** with overwrite confirmation
- ✅ **One-way sync** between databases
- ✅ **Connection testing** before operations
- ✅ **Progress indicators** and status messages
- ✅ **File dialogs** for easy file/folder selection

## 🖥️ **System Requirements**

### **Minimum Requirements**
- **Windows**: Windows 10 or later (64-bit)
- **Linux**: Ubuntu 18.04+ or equivalent (64-bit)
- **macOS**: macOS 10.15 (Catalina) or later (64-bit)
- **RAM**: 512 MB minimum, 1 GB recommended
- **Disk Space**: 200 MB for application files

### **Recommended**
- **RAM**: 2 GB or more
- **Disk Space**: 1 GB+ for backup operations
- **Network**: For SMB share access

## 🛠️ **Troubleshooting**

### **Windows**
- If Windows Defender blocks the app, click "More info" → "Run anyway"
- Run installer as administrator for full functionality
- Ensure database client tools are in system PATH

### **Linux**
- If app won't start, check execute permissions: `chmod +x DatabaseBackupApp`
- For SMB shares, install cifs-utils: `sudo apt install cifs-utils`
- Some distributions may require additional GUI libraries

### **macOS**
- If macOS blocks the app, go to System Preferences → Security & Privacy → Allow
- You may need to right-click the app and select "Open" the first time
- Ensure database client tools are installed via Homebrew

## 📞 **Support**

### **Common Issues**
1. **Connection Failed**: Check server address, port, and credentials
2. **Backup Failed**: Verify disk space and write permissions
3. **Restore Failed**: Ensure backup file format matches database type
4. **SMB Access Denied**: Check network connectivity and SMB credentials

### **Database Tool Installation**
- **MySQL**: Download from mysql.com or use package manager
- **PostgreSQL**: Download from postgresql.org or use package manager  
- **SQL Server**: Download SQL Server tools from Microsoft

## 📄 **File Sizes**

- **Windows executable**: ~80-100 MB (self-contained)
- **Linux executable**: ~80-100 MB (self-contained)
- **macOS executable**: ~80-100 MB (self-contained)

## 🔐 **Security Notes**

- Application does not store passwords permanently
- SMB credentials are used only during file operations
- All database connections use secure protocols when available
- No telemetry or data collection

## 📝 **License**

This software is provided as-is for educational and development purposes.

---

**Enjoy using the Database Backup & Restore Tool!** 🎉
