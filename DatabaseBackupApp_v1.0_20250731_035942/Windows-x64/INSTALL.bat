@echo off
echo ========================================
echo Database Backup ^& Restore Tool Installer
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo WARNING: Not running as administrator. Some features may not work properly.
    echo Please run as administrator for full functionality.
    echo.
)

REM Set installation directory
set "INSTALL_DIR=%ProgramFiles%\DatabaseBackupApp"
set "DESKTOP_SHORTCUT=%USERPROFILE%\Desktop\Database Backup Tool.lnk"
set "START_MENU_SHORTCUT=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Database Backup Tool.lnk"

echo Installing Database Backup ^& Restore Tool...
echo Installation directory: %INSTALL_DIR%
echo.

REM Create installation directory
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo Created installation directory.
)

REM Copy files
echo Copying application files...
xcopy /Y /Q "DatabaseBackupApp.exe" "%INSTALL_DIR%\"
xcopy /Y /Q "*.dll" "%INSTALL_DIR%\"
xcopy /Y /Q "*.pdb" "%INSTALL_DIR%\" 2>nul

echo Application files copied successfully.
echo.

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\DatabaseBackupApp.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Database Backup and Restore Tool'; $Shortcut.Save()"

REM Create start menu shortcut
echo Creating start menu shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\DatabaseBackupApp.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Database Backup and Restore Tool'; $Shortcut.Save()"

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo The application has been installed to: %INSTALL_DIR%
echo Desktop shortcut created: %DESKTOP_SHORTCUT%
echo Start menu shortcut created: %START_MENU_SHORTCUT%
echo.
echo You can now run the application from:
echo - Desktop shortcut
echo - Start menu
echo - Command line: "%INSTALL_DIR%\DatabaseBackupApp.exe"
echo.
echo Prerequisites for full functionality:
echo - SQL Server client tools (for SQL Server backups)
echo - MySQL client tools (for MySQL backups)  
echo - PostgreSQL client tools (for PostgreSQL backups)
echo.
pause
