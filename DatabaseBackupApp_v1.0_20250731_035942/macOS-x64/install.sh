#!/bin/bash

echo "========================================"
echo "Database Backup & Restore Tool Installer"
echo "========================================"
echo

# Set installation paths
INSTALL_DIR="/Applications/DatabaseBackupApp"
APP_BUNDLE="$INSTALL_DIR/Database Backup Tool.app"
BIN_LINK="/usr/local/bin/database-backup-tool"

echo "Installing Database Backup & Restore Tool..."
echo "Installation directory: $INSTALL_DIR"
echo

# Create installation directory
sudo mkdir -p "$INSTALL_DIR"
echo "Created installation directory."

# Copy files
echo "Copying application files..."
sudo cp DatabaseBackupApp "$INSTALL_DIR/"
sudo cp *.dylib "$INSTALL_DIR/" 2>/dev/null || true
sudo cp *.dll "$INSTALL_DIR/" 2>/dev/null || true
sudo cp createdump "$INSTALL_DIR/" 2>/dev/null || true

# Make executable
sudo chmod +x "$INSTALL_DIR/DatabaseBackupApp"
echo "Application files copied successfully."

# Create macOS app bundle structure
echo "Creating macOS app bundle..."
sudo mkdir -p "$APP_BUNDLE/Contents/MacOS"
sudo mkdir -p "$APP_BUNDLE/Contents/Resources"

# Create Info.plist
sudo cat > "$APP_BUNDLE/Contents/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>DatabaseBackupApp</string>
    <key>CFBundleIdentifier</key>
    <string>com.databasebackupapp.app</string>
    <key>CFBundleName</key>
    <string>Database Backup Tool</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    <key>NSHighResolutionCapable</key>
    <true/>
</dict>
</plist>
EOF

# Copy executable to app bundle
sudo cp "$INSTALL_DIR/DatabaseBackupApp" "$APP_BUNDLE/Contents/MacOS/"
sudo cp "$INSTALL_DIR"/*.dylib "$APP_BUNDLE/Contents/MacOS/" 2>/dev/null || true
sudo chmod +x "$APP_BUNDLE/Contents/MacOS/DatabaseBackupApp"

echo "App bundle created."

# Create command line shortcut
echo "Creating command line shortcut..."
if sudo ln -sf "$INSTALL_DIR/DatabaseBackupApp" "$BIN_LINK" 2>/dev/null; then
    echo "Command line shortcut created: $BIN_LINK"
else
    echo "Cannot create command line shortcut (insufficient permissions)"
fi

echo
echo "========================================"
echo "Installation completed successfully!"
echo "========================================"
echo
echo "The application has been installed to: $INSTALL_DIR"
echo "App bundle created: $APP_BUNDLE"
echo
echo "You can now run the application:"
echo "- Double-click the app bundle in Applications folder"
echo "- Command line: $INSTALL_DIR/DatabaseBackupApp"
if [ -L "$BIN_LINK" ]; then
    echo "- Command line shortcut: database-backup-tool"
fi
echo
echo "Prerequisites for full functionality:"
echo "- MySQL client tools: brew install mysql-client"
echo "- PostgreSQL client tools: brew install postgresql"
echo "- SQL Server tools: Install mssql-tools via Homebrew"
echo
echo "Note: You may need to allow the app in System Preferences > Security & Privacy"
echo "if macOS blocks it on first run."
echo
echo "Installation complete!"
