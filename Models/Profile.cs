using System;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace DatabaseBackupApp.Models
{
    public class Profile : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private string _description = string.Empty;
        private DateTime _createdDate = DateTime.Now;
        private DateTime _lastModified = DateTime.Now;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
                UpdateLastModified();
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged(nameof(Description));
                UpdateLastModified();
            }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set
            {
                _createdDate = value;
                OnPropertyChanged(nameof(CreatedDate));
            }
        }

        public DateTime LastModified
        {
            get => _lastModified;
            set
            {
                _lastModified = value;
                OnPropertyChanged(nameof(LastModified));
            }
        }

        // Database connection settings (passwords will be excluded from serialization)
        public ProfileDatabaseConnection SourceConnection { get; set; } = new();
        public ProfileDatabaseConnection TargetConnection { get; set; } = new();
        
        // Backup settings (SMB password will be excluded from serialization)
        public ProfileBackupSettings BackupSettings { get; set; } = new();

        private void UpdateLastModified()
        {
            LastModified = DateTime.Now;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // Profile-specific database connection that excludes passwords from serialization
    public class ProfileDatabaseConnection : INotifyPropertyChanged
    {
        private DatabaseType _databaseType = DatabaseType.SqlServer;
        private string _server = string.Empty;
        private int _port = 1433;
        private string _database = string.Empty;
        private string _username = string.Empty;
        private bool _useWindowsAuth = false;

        public DatabaseType DatabaseType
        {
            get => _databaseType;
            set
            {
                _databaseType = value;
                OnPropertyChanged(nameof(DatabaseType));
            }
        }

        public string Server
        {
            get => _server;
            set
            {
                _server = value;
                OnPropertyChanged(nameof(Server));
            }
        }

        public int Port
        {
            get => _port;
            set
            {
                _port = value;
                OnPropertyChanged(nameof(Port));
            }
        }

        public string Database
        {
            get => _database;
            set
            {
                _database = value;
                OnPropertyChanged(nameof(Database));
            }
        }

        public string Username
        {
            get => _username;
            set
            {
                _username = value;
                OnPropertyChanged(nameof(Username));
            }
        }

        public bool UseWindowsAuth
        {
            get => _useWindowsAuth;
            set
            {
                _useWindowsAuth = value;
                OnPropertyChanged(nameof(UseWindowsAuth));
            }
        }

        // Password is excluded from JSON serialization for security
        [JsonIgnore]
        public string Password { get; set; } = string.Empty;

        // Method to copy from regular DatabaseConnection
        public void CopyFrom(DatabaseConnection connection)
        {
            DatabaseType = connection.DatabaseType;
            Server = connection.Server;
            Port = connection.Port;
            Database = connection.Database;
            Username = connection.Username;
            UseWindowsAuth = connection.UseWindowsAuth;
            // Password is intentionally not copied for security
        }

        // Method to copy to regular DatabaseConnection
        public void CopyTo(DatabaseConnection connection)
        {
            connection.DatabaseType = DatabaseType;
            connection.Server = Server;
            connection.Port = Port;
            connection.Database = Database;
            connection.Username = Username;
            connection.UseWindowsAuth = UseWindowsAuth;
            // Password must be set separately after loading
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // Profile-specific backup settings that excludes SMB password from serialization
    public class ProfileBackupSettings : INotifyPropertyChanged
    {
        private string _localPath = string.Empty;
        private string _smbPath = string.Empty;
        private string _smbUsername = string.Empty;
        private BackupDestination _destination = BackupDestination.Local;
        private string _customFileName = string.Empty;
        private bool _includeTimestamp = true;

        public string LocalPath
        {
            get => _localPath;
            set
            {
                _localPath = value;
                OnPropertyChanged(nameof(LocalPath));
            }
        }

        public string SmbPath
        {
            get => _smbPath;
            set
            {
                _smbPath = value;
                OnPropertyChanged(nameof(SmbPath));
            }
        }

        public string SmbUsername
        {
            get => _smbUsername;
            set
            {
                _smbUsername = value;
                OnPropertyChanged(nameof(SmbUsername));
            }
        }

        public BackupDestination Destination
        {
            get => _destination;
            set
            {
                _destination = value;
                OnPropertyChanged(nameof(Destination));
            }
        }

        public string CustomFileName
        {
            get => _customFileName;
            set
            {
                _customFileName = value;
                OnPropertyChanged(nameof(CustomFileName));
            }
        }

        public bool IncludeTimestamp
        {
            get => _includeTimestamp;
            set
            {
                _includeTimestamp = value;
                OnPropertyChanged(nameof(IncludeTimestamp));
            }
        }

        // SMB Password is excluded from JSON serialization for security
        [JsonIgnore]
        public string SmbPassword { get; set; } = string.Empty;

        // Method to copy from regular BackupSettings
        public void CopyFrom(BackupSettings settings)
        {
            LocalPath = settings.LocalPath;
            SmbPath = settings.SmbPath;
            SmbUsername = settings.SmbUsername;
            Destination = settings.Destination;
            CustomFileName = settings.CustomFileName;
            IncludeTimestamp = settings.IncludeTimestamp;
            // SmbPassword is intentionally not copied for security
        }

        // Method to copy to regular BackupSettings
        public void CopyTo(BackupSettings settings)
        {
            settings.LocalPath = LocalPath;
            settings.SmbPath = SmbPath;
            settings.SmbUsername = SmbUsername;
            settings.Destination = Destination;
            settings.CustomFileName = CustomFileName;
            settings.IncludeTimestamp = IncludeTimestamp;
            // SmbPassword must be set separately after loading
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
