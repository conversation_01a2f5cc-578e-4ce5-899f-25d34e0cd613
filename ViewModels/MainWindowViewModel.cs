using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Avalonia.Controls;
using DatabaseBackupApp.Models;
using DatabaseBackupApp.Services;

namespace DatabaseBackupApp.ViewModels
{
    public class MainWindowViewModel : INotifyPropertyChanged
    {
        private readonly DatabaseService _databaseService;
        private readonly FileDialogService _fileDialogService;
        private readonly ProfileService _profileService;
        private string _statusMessage = "Ready";
        private bool _isOperationInProgress;
        private Window? _parentWindow;
        private Profile? _selectedProfile;

        public MainWindowViewModel()
        {
            _databaseService = new DatabaseService();
            _fileDialogService = new FileDialogService();
            _profileService = new ProfileService();
            
            SourceConnection = new DatabaseConnection();
            TargetConnection = new DatabaseConnection();
            BackupSettings = new BackupSettings();
            
            DatabaseTypes = new ObservableCollection<DatabaseType>
            {
                DatabaseType.SqlServer,
                DatabaseType.MySQL,
                DatabaseType.PostgreSQL
            };
            
            BackupDestinations = new ObservableCollection<BackupDestination>
            {
                BackupDestination.Local,
                BackupDestination.SMB
            };

            Profiles = new ObservableCollection<Profile>();

            TestSourceConnectionCommand = new RelayCommand(async () => await TestSourceConnection());
            TestTargetConnectionCommand = new RelayCommand(async () => await TestTargetConnection());
            BackupDatabaseCommand = new RelayCommand(async () => await BackupDatabase());
            RestoreDatabaseCommand = new RelayCommand(async () => await RestoreDatabase());
            SyncDatabasesCommand = new RelayCommand(async () => await SyncDatabases());
            BrowseLocalPathCommand = new RelayCommand(async () => await BrowseLocalPath());
            BrowseBackupFileCommand = new RelayCommand(async () => await BrowseBackupFile());

            // Profile management commands
            LoadProfileCommand = new RelayCommand(async () => await LoadProfile());
            SaveProfileCommand = new RelayCommand(async () => await SaveProfile());
            SaveAsProfileCommand = new RelayCommand(async () => await SaveAsProfile());
            DeleteProfileCommand = new RelayCommand(async () => await DeleteProfile());

            // Load profiles on startup
            _ = LoadProfilesAsync();
        }

        public void SetParentWindow(Window window)
        {
            _parentWindow = window;
        }

        public DatabaseConnection SourceConnection { get; }
        public DatabaseConnection TargetConnection { get; }
        public BackupSettings BackupSettings { get; }

        public ObservableCollection<DatabaseType> DatabaseTypes { get; }
        public ObservableCollection<BackupDestination> BackupDestinations { get; }
        public ObservableCollection<Profile> Profiles { get; }

        public Profile? SelectedProfile
        {
            get => _selectedProfile;
            set
            {
                _selectedProfile = value;
                OnPropertyChanged(nameof(SelectedProfile));
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged(nameof(StatusMessage));
            }
        }

        public bool IsOperationInProgress
        {
            get => _isOperationInProgress;
            set
            {
                _isOperationInProgress = value;
                OnPropertyChanged(nameof(IsOperationInProgress));
            }
        }

        private string _backupFilePath = string.Empty;
        public string BackupFilePath
        {
            get => _backupFilePath;
            set
            {
                _backupFilePath = value;
                OnPropertyChanged(nameof(BackupFilePath));
            }
        }

        public ICommand TestSourceConnectionCommand { get; }
        public ICommand TestTargetConnectionCommand { get; }
        public ICommand BackupDatabaseCommand { get; }
        public ICommand RestoreDatabaseCommand { get; }
        public ICommand SyncDatabasesCommand { get; }
        public ICommand BrowseLocalPathCommand { get; }
        public ICommand BrowseBackupFileCommand { get; }

        // Profile management commands
        public ICommand LoadProfileCommand { get; }
        public ICommand SaveProfileCommand { get; }
        public ICommand SaveAsProfileCommand { get; }
        public ICommand DeleteProfileCommand { get; }

        private async Task TestSourceConnection()
        {
            IsOperationInProgress = true;
            StatusMessage = "Testing source connection...";
            
            try
            {
                var result = await _databaseService.TestConnectionAsync(SourceConnection);
                StatusMessage = result ? "Source connection successful!" : "Source connection failed!";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Source connection error: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task TestTargetConnection()
        {
            IsOperationInProgress = true;
            StatusMessage = "Testing target connection...";
            
            try
            {
                var result = await _databaseService.TestConnectionAsync(TargetConnection);
                StatusMessage = result ? "Target connection successful!" : "Target connection failed!";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Target connection error: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task BackupDatabase()
        {
            IsOperationInProgress = true;
            StatusMessage = "Creating backup...";
            
            try
            {
                var backupPath = await _databaseService.BackupDatabaseAsync(SourceConnection, BackupSettings);
                StatusMessage = $"Backup completed successfully: {backupPath}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Backup failed: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task RestoreDatabase()
        {
            if (string.IsNullOrEmpty(BackupFilePath))
            {
                StatusMessage = "Please select a backup file first.";
                return;
            }

            IsOperationInProgress = true;
            StatusMessage = "Restoring database...";

            try
            {
                // Check if backup file is on SMB share
                BackupSettings? smbSettings = null;
                if (BackupFilePath.StartsWith("\\\\") || BackupFilePath.StartsWith("//"))
                {
                    smbSettings = BackupSettings;
                }

                var result = await _databaseService.RestoreDatabaseAsync(TargetConnection, BackupFilePath, smbSettings);
                StatusMessage = result ? "Database restored successfully!" : "Database restore failed!";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Restore failed: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task SyncDatabases()
        {
            IsOperationInProgress = true;
            StatusMessage = "Synchronizing databases...";
            
            try
            {
                var tempSettings = new BackupSettings
                {
                    Destination = BackupDestination.Local,
                    LocalPath = Path.GetTempPath()
                };
                
                var result = await _databaseService.SyncDatabasesAsync(SourceConnection, TargetConnection, tempSettings);
                StatusMessage = result ? "Database synchronization completed!" : "Database synchronization failed!";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Sync failed: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task BrowseLocalPath()
        {
            if (_parentWindow == null)
            {
                StatusMessage = "Window not available for file dialog.";
                return;
            }

            try
            {
                var selectedPath = await _fileDialogService.OpenFolderDialogAsync(_parentWindow);
                if (!string.IsNullOrEmpty(selectedPath))
                {
                    BackupSettings.LocalPath = selectedPath;
                    StatusMessage = $"Local path set to: {BackupSettings.LocalPath}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error selecting folder: {ex.Message}";
            }
        }

        private async Task BrowseBackupFile()
        {
            if (_parentWindow == null)
            {
                StatusMessage = "Window not available for file dialog.";
                return;
            }

            try
            {
                var selectedFile = await _fileDialogService.OpenFileDialogAsync(_parentWindow);
                if (!string.IsNullOrEmpty(selectedFile))
                {
                    BackupFilePath = selectedFile;
                    StatusMessage = $"Backup file selected: {Path.GetFileName(BackupFilePath)}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error selecting file: {ex.Message}";
            }
        }

        // Profile management methods
        private async Task LoadProfilesAsync()
        {
            try
            {
                var profiles = await _profileService.GetAllProfilesAsync();
                Profiles.Clear();
                foreach (var profile in profiles)
                {
                    Profiles.Add(profile);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading profiles: {ex.Message}";
            }
        }

        private async Task LoadProfile()
        {
            if (SelectedProfile == null)
            {
                StatusMessage = "Please select a profile to load.";
                return;
            }

            IsOperationInProgress = true;
            StatusMessage = "Loading profile...";

            try
            {
                var profile = await _profileService.LoadProfileAsync(SelectedProfile.Name);
                if (profile != null)
                {
                    // Copy profile data to current connections and settings
                    profile.SourceConnection.CopyTo(SourceConnection);
                    profile.TargetConnection.CopyTo(TargetConnection);
                    profile.BackupSettings.CopyTo(BackupSettings);

                    // Set passwords from the loaded profile
                    SourceConnection.Password = profile.SourceConnection.Password;
                    TargetConnection.Password = profile.TargetConnection.Password;
                    BackupSettings.SmbPassword = profile.BackupSettings.SmbPassword;

                    StatusMessage = $"Profile '{profile.Name}' loaded successfully!";
                }
                else
                {
                    StatusMessage = "Failed to load profile.";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading profile: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task SaveProfile()
        {
            if (SelectedProfile == null)
            {
                await SaveAsProfile();
                return;
            }

            IsOperationInProgress = true;
            StatusMessage = "Saving profile...";

            try
            {
                var success = await _profileService.SaveProfileAsync(SelectedProfile, SourceConnection, TargetConnection, BackupSettings);
                if (success)
                {
                    StatusMessage = $"Profile '{SelectedProfile.Name}' saved successfully!";
                }
                else
                {
                    StatusMessage = "Failed to save profile.";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error saving profile: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task SaveAsProfile()
        {
            if (_parentWindow == null)
            {
                StatusMessage = "Window not available for dialog.";
                return;
            }

            // Simple input dialog using a text box (in a real app, you'd use a proper dialog)
            var dialog = new Window
            {
                Title = "Save Profile As",
                Width = 400,
                Height = 200,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };

            var stackPanel = new StackPanel { Margin = new Avalonia.Thickness(20) };
            stackPanel.Children.Add(new TextBlock { Text = "Profile Name:", Margin = new Avalonia.Thickness(0, 0, 0, 10) });

            var nameTextBox = new TextBox { Margin = new Avalonia.Thickness(0, 0, 0, 10) };
            stackPanel.Children.Add(nameTextBox);

            stackPanel.Children.Add(new TextBlock { Text = "Description (optional):", Margin = new Avalonia.Thickness(0, 10, 0, 10) });

            var descTextBox = new TextBox { Margin = new Avalonia.Thickness(0, 0, 0, 20) };
            stackPanel.Children.Add(descTextBox);

            var buttonPanel = new StackPanel { Orientation = Avalonia.Layout.Orientation.Horizontal, HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Right };

            var saveButton = new Button { Content = "Save", Margin = new Avalonia.Thickness(0, 0, 10, 0) };
            var cancelButton = new Button { Content = "Cancel" };

            buttonPanel.Children.Add(saveButton);
            buttonPanel.Children.Add(cancelButton);
            stackPanel.Children.Add(buttonPanel);

            dialog.Content = stackPanel;

            string? profileName = null;
            string? profileDescription = null;

            saveButton.Click += (s, e) =>
            {
                profileName = nameTextBox.Text?.Trim();
                profileDescription = descTextBox.Text?.Trim();
                dialog.Close();
            };

            cancelButton.Click += (s, e) => dialog.Close();

            await dialog.ShowDialog(_parentWindow);

            if (!string.IsNullOrWhiteSpace(profileName))
            {
                IsOperationInProgress = true;
                StatusMessage = "Saving profile...";

                try
                {
                    var newProfile = new Profile
                    {
                        Name = profileName,
                        Description = profileDescription ?? string.Empty
                    };

                    var success = await _profileService.SaveProfileAsync(newProfile, SourceConnection, TargetConnection, BackupSettings);
                    if (success)
                    {
                        await LoadProfilesAsync();
                        SelectedProfile = Profiles.FirstOrDefault(p => p.Name == profileName);
                        StatusMessage = $"Profile '{profileName}' saved successfully!";
                    }
                    else
                    {
                        StatusMessage = "Failed to save profile.";
                    }
                }
                catch (Exception ex)
                {
                    StatusMessage = $"Error saving profile: {ex.Message}";
                }
                finally
                {
                    IsOperationInProgress = false;
                }
            }
        }

        private async Task DeleteProfile()
        {
            if (SelectedProfile == null)
            {
                StatusMessage = "Please select a profile to delete.";
                return;
            }

            if (_parentWindow == null)
            {
                StatusMessage = "Window not available for confirmation dialog.";
                return;
            }

            // Simple confirmation dialog
            var result = await ShowConfirmationDialog($"Are you sure you want to delete the profile '{SelectedProfile.Name}'?");

            if (result)
            {
                IsOperationInProgress = true;
                StatusMessage = "Deleting profile...";

                try
                {
                    var success = await _profileService.DeleteProfileAsync(SelectedProfile.Name);
                    if (success)
                    {
                        await LoadProfilesAsync();
                        SelectedProfile = null;
                        StatusMessage = "Profile deleted successfully!";
                    }
                    else
                    {
                        StatusMessage = "Failed to delete profile.";
                    }
                }
                catch (Exception ex)
                {
                    StatusMessage = $"Error deleting profile: {ex.Message}";
                }
                finally
                {
                    IsOperationInProgress = false;
                }
            }
        }

        private async Task<bool> ShowConfirmationDialog(string message)
        {
            if (_parentWindow == null) return false;

            var dialog = new Window
            {
                Title = "Confirm",
                Width = 350,
                Height = 150,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };

            var stackPanel = new StackPanel { Margin = new Avalonia.Thickness(20) };
            stackPanel.Children.Add(new TextBlock { Text = message, Margin = new Avalonia.Thickness(0, 0, 0, 20), TextWrapping = Avalonia.Media.TextWrapping.Wrap });

            var buttonPanel = new StackPanel { Orientation = Avalonia.Layout.Orientation.Horizontal, HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Right };

            var yesButton = new Button { Content = "Yes", Margin = new Avalonia.Thickness(0, 0, 10, 0) };
            var noButton = new Button { Content = "No" };

            buttonPanel.Children.Add(yesButton);
            buttonPanel.Children.Add(noButton);
            stackPanel.Children.Add(buttonPanel);

            dialog.Content = stackPanel;

            bool result = false;

            yesButton.Click += (s, e) =>
            {
                result = true;
                dialog.Close();
            };

            noButton.Click += (s, e) => dialog.Close();

            await dialog.ShowDialog(_parentWindow);
            return result;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged;

        public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object? parameter) => _execute();

        public void RaiseCanExecuteChanged() => CanExecuteChanged?.Invoke(this, EventArgs.Empty);
    }
}
