using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Input;
using Avalonia.Controls;
using DatabaseBackupApp.Models;
using DatabaseBackupApp.Services;

namespace DatabaseBackupApp.ViewModels
{
    public class MainWindowViewModel : INotifyPropertyChanged
    {
        private readonly DatabaseService _databaseService;
        private readonly FileDialogService _fileDialogService;
        private string _statusMessage = "Ready";
        private bool _isOperationInProgress;
        private Window? _parentWindow;

        public MainWindowViewModel()
        {
            _databaseService = new DatabaseService();
            _fileDialogService = new FileDialogService();
            
            SourceConnection = new DatabaseConnection();
            TargetConnection = new DatabaseConnection();
            BackupSettings = new BackupSettings();
            
            DatabaseTypes = new ObservableCollection<DatabaseType>
            {
                DatabaseType.SqlServer,
                DatabaseType.MySQL,
                DatabaseType.PostgreSQL
            };
            
            BackupDestinations = new ObservableCollection<BackupDestination>
            {
                BackupDestination.Local,
                BackupDestination.SMB
            };

            TestSourceConnectionCommand = new RelayCommand(async () => await TestSourceConnection());
            TestTargetConnectionCommand = new RelayCommand(async () => await TestTargetConnection());
            BackupDatabaseCommand = new RelayCommand(async () => await BackupDatabase());
            RestoreDatabaseCommand = new RelayCommand(async () => await RestoreDatabase());
            SyncDatabasesCommand = new RelayCommand(async () => await SyncDatabases());
            BrowseLocalPathCommand = new RelayCommand(async () => await BrowseLocalPath());
            BrowseBackupFileCommand = new RelayCommand(async () => await BrowseBackupFile());
        }

        public void SetParentWindow(Window window)
        {
            _parentWindow = window;
        }

        public DatabaseConnection SourceConnection { get; }
        public DatabaseConnection TargetConnection { get; }
        public BackupSettings BackupSettings { get; }
        
        public ObservableCollection<DatabaseType> DatabaseTypes { get; }
        public ObservableCollection<BackupDestination> BackupDestinations { get; }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged(nameof(StatusMessage));
            }
        }

        public bool IsOperationInProgress
        {
            get => _isOperationInProgress;
            set
            {
                _isOperationInProgress = value;
                OnPropertyChanged(nameof(IsOperationInProgress));
            }
        }

        private string _backupFilePath = string.Empty;
        public string BackupFilePath
        {
            get => _backupFilePath;
            set
            {
                _backupFilePath = value;
                OnPropertyChanged(nameof(BackupFilePath));
            }
        }

        public ICommand TestSourceConnectionCommand { get; }
        public ICommand TestTargetConnectionCommand { get; }
        public ICommand BackupDatabaseCommand { get; }
        public ICommand RestoreDatabaseCommand { get; }
        public ICommand SyncDatabasesCommand { get; }
        public ICommand BrowseLocalPathCommand { get; }
        public ICommand BrowseBackupFileCommand { get; }

        private async Task TestSourceConnection()
        {
            IsOperationInProgress = true;
            StatusMessage = "Testing source connection...";
            
            try
            {
                var result = await _databaseService.TestConnectionAsync(SourceConnection);
                StatusMessage = result ? "Source connection successful!" : "Source connection failed!";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Source connection error: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task TestTargetConnection()
        {
            IsOperationInProgress = true;
            StatusMessage = "Testing target connection...";
            
            try
            {
                var result = await _databaseService.TestConnectionAsync(TargetConnection);
                StatusMessage = result ? "Target connection successful!" : "Target connection failed!";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Target connection error: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task BackupDatabase()
        {
            IsOperationInProgress = true;
            StatusMessage = "Creating backup...";
            
            try
            {
                var backupPath = await _databaseService.BackupDatabaseAsync(SourceConnection, BackupSettings);
                StatusMessage = $"Backup completed successfully: {backupPath}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Backup failed: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task RestoreDatabase()
        {
            if (string.IsNullOrEmpty(BackupFilePath))
            {
                StatusMessage = "Please select a backup file first.";
                return;
            }

            IsOperationInProgress = true;
            StatusMessage = "Restoring database...";

            try
            {
                // Check if backup file is on SMB share
                BackupSettings? smbSettings = null;
                if (BackupFilePath.StartsWith("\\\\") || BackupFilePath.StartsWith("//"))
                {
                    smbSettings = BackupSettings;
                }

                var result = await _databaseService.RestoreDatabaseAsync(TargetConnection, BackupFilePath, smbSettings);
                StatusMessage = result ? "Database restored successfully!" : "Database restore failed!";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Restore failed: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task SyncDatabases()
        {
            IsOperationInProgress = true;
            StatusMessage = "Synchronizing databases...";
            
            try
            {
                var tempSettings = new BackupSettings
                {
                    Destination = BackupDestination.Local,
                    LocalPath = Path.GetTempPath()
                };
                
                var result = await _databaseService.SyncDatabasesAsync(SourceConnection, TargetConnection, tempSettings);
                StatusMessage = result ? "Database synchronization completed!" : "Database synchronization failed!";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Sync failed: {ex.Message}";
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        private async Task BrowseLocalPath()
        {
            if (_parentWindow == null)
            {
                StatusMessage = "Window not available for file dialog.";
                return;
            }

            try
            {
                var selectedPath = await _fileDialogService.OpenFolderDialogAsync(_parentWindow);
                if (!string.IsNullOrEmpty(selectedPath))
                {
                    BackupSettings.LocalPath = selectedPath;
                    StatusMessage = $"Local path set to: {BackupSettings.LocalPath}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error selecting folder: {ex.Message}";
            }
        }

        private async Task BrowseBackupFile()
        {
            if (_parentWindow == null)
            {
                StatusMessage = "Window not available for file dialog.";
                return;
            }

            try
            {
                var selectedFile = await _fileDialogService.OpenFileDialogAsync(_parentWindow);
                if (!string.IsNullOrEmpty(selectedFile))
                {
                    BackupFilePath = selectedFile;
                    StatusMessage = $"Backup file selected: {Path.GetFileName(BackupFilePath)}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error selecting file: {ex.Message}";
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged;

        public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object? parameter) => _execute();

        public void RaiseCanExecuteChanged() => CanExecuteChanged?.Invoke(this, EventArgs.Empty);
    }
}
