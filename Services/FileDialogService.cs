using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Platform.Storage;

namespace DatabaseBackupApp.Services
{
    public class FileDialogService
    {
        public async Task<string?> OpenFolderDialogAsync(Window parent)
        {
            try
            {
                var folders = await parent.StorageProvider.OpenFolderPickerAsync(new FolderPickerOpenOptions
                {
                    Title = "Select Backup Folder",
                    AllowMultiple = false
                });

                return folders.Count > 0 ? folders[0].Path.LocalPath : null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<string?> OpenFileDialogAsync(Window parent)
        {
            try
            {
                var files = await parent.StorageProvider.OpenFilePickerAsync(new FilePickerOpenOptions
                {
                    Title = "Select Backup File",
                    AllowMultiple = false,
                    FileTypeFilter = new[]
                    {
                        new FilePickerFileType("SQL Server Backup")
                        {
                            Patterns = new[] { "*.bak" }
                        },
                        new FilePickerFileType("SQL Script")
                        {
                            Patterns = new[] { "*.sql" }
                        },
                        new FilePickerFileType("All Files")
                        {
                            Patterns = new[] { "*.*" }
                        }
                    }
                });

                return files.Count > 0 ? files[0].Path.LocalPath : null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<string?> SaveFileDialogAsync(Window parent, string defaultFileName = "backup")
        {
            try
            {
                var file = await parent.StorageProvider.SaveFilePickerAsync(new FilePickerSaveOptions
                {
                    Title = "Save Backup As",
                    SuggestedFileName = defaultFileName,
                    FileTypeChoices = new[]
                    {
                        new FilePickerFileType("SQL Server Backup")
                        {
                            Patterns = new[] { "*.bak" }
                        },
                        new FilePickerFileType("SQL Script")
                        {
                            Patterns = new[] { "*.sql" }
                        },
                        new FilePickerFileType("All Files")
                        {
                            Patterns = new[] { "*.*" }
                        }
                    }
                });

                return file?.Path.LocalPath;
            }
            catch
            {
                return null;
            }
        }
    }
}
