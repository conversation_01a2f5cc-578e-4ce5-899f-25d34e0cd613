using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseBackupApp.Models;

namespace DatabaseBackupApp.Services
{
    public class BackupSettingsService
    {
        private readonly string _settingsPath;

        public BackupSettingsService()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var appFolder = Path.Combine(appDataPath, "DatabaseBackupApp");
            Directory.CreateDirectory(appFolder);
            _settingsPath = Path.Combine(appFolder, "backup_settings.json");
        }

        public async Task<List<BackupSettingsProfile>> LoadSettingsAsync()
        {
            if (!File.Exists(_settingsPath))
                return new List<BackupSettingsProfile>();

            try
            {
                var json = await File.ReadAllTextAsync(_settingsPath);
                return JsonSerializer.Deserialize<List<BackupSettingsProfile>>(json) ?? new List<BackupSettingsProfile>();
            }
            catch
            {
                return new List<BackupSettingsProfile>();
            }
        }

        public async Task SaveSettingsAsync(List<BackupSettingsProfile> settings)
        {
            try
            {
                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_settingsPath, json);
            }
            catch
            {
                // Handle save error silently
            }
        }
    }

    public class BackupSettingsProfile
    {
        public string Name { get; set; } = string.Empty;
        public string LocalPath { get; set; } = string.Empty;
        public string SmbPath { get; set; } = string.Empty;
        public string SmbUsername { get; set; } = string.Empty;
        public BackupDestination Destination { get; set; }
        public string CustomFileName { get; set; } = string.Empty;
        public bool UseCustomFileName { get; set; }
        // Note: SMB Password is intentionally NOT included for security
    }
}