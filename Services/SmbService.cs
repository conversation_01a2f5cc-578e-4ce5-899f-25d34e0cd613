using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

namespace DatabaseBackupApp.Services
{
    public class SmbService
    {
        public async Task<bool> ConnectToSmbShareAsync(string smbPath, string username, string password)
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    return await ConnectWindowsSmbAsync(smbPath, username, password);
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    return await ConnectLinuxSmbAsync(smbPath, username, password);
                }
                else
                {
                    throw new PlatformNotSupportedException("SMB connections are only supported on Windows and Linux");
                }
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DisconnectFromSmbShareAsync(string smbPath)
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    return await DisconnectWindowsSmbAsync(smbPath);
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    return await DisconnectLinuxSmbAsync(smbPath);
                }
                else
                {
                    throw new PlatformNotSupportedException("SMB connections are only supported on Windows and Linux");
                }
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> CopyFileToSmbAsync(string localFilePath, string smbPath, string username, string password)
        {
            try
            {
                // Connect to SMB share first
                var connected = await ConnectToSmbShareAsync(smbPath, username, password);
                if (!connected)
                {
                    throw new InvalidOperationException("Failed to connect to SMB share");
                }

                var fileName = Path.GetFileName(localFilePath);
                var destinationPath = Path.Combine(smbPath, fileName);

                // Copy the file
                File.Copy(localFilePath, destinationPath, true);

                return destinationPath;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to copy file to SMB share: {ex.Message}", ex);
            }
        }

        public async Task<string> CopyFileFromSmbAsync(string smbFilePath, string localPath, string username, string password)
        {
            try
            {
                var smbDirectory = Path.GetDirectoryName(smbFilePath) ?? "";
                
                // Connect to SMB share first
                var connected = await ConnectToSmbShareAsync(smbDirectory, username, password);
                if (!connected)
                {
                    throw new InvalidOperationException("Failed to connect to SMB share");
                }

                var fileName = Path.GetFileName(smbFilePath);
                var destinationPath = Path.Combine(localPath, fileName);

                // Copy the file
                File.Copy(smbFilePath, destinationPath, true);

                return destinationPath;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to copy file from SMB share: {ex.Message}", ex);
            }
        }

        private async Task<bool> ConnectWindowsSmbAsync(string smbPath, string username, string password)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "net",
                    Arguments = $"use \"{smbPath}\" /user:{username} {password}",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(startInfo);
                if (process == null) return false;

                await process.WaitForExitAsync();
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> DisconnectWindowsSmbAsync(string smbPath)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "net",
                    Arguments = $"use \"{smbPath}\" /delete",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(startInfo);
                if (process == null) return false;

                await process.WaitForExitAsync();
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> ConnectLinuxSmbAsync(string smbPath, string username, string password)
        {
            try
            {
                // Create a temporary mount point
                var mountPoint = Path.Combine("/tmp", $"smb_mount_{Guid.NewGuid():N}");
                Directory.CreateDirectory(mountPoint);

                var startInfo = new ProcessStartInfo
                {
                    FileName = "mount",
                    Arguments = $"-t cifs \"{smbPath}\" \"{mountPoint}\" -o username={username},password={password}",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(startInfo);
                if (process == null) return false;

                await process.WaitForExitAsync();
                
                if (process.ExitCode == 0)
                {
                    // Store the mount point for later cleanup
                    _mountPoints[smbPath] = mountPoint;
                    return true;
                }
                else
                {
                    // Clean up the directory if mount failed
                    try { Directory.Delete(mountPoint); } catch { }
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> DisconnectLinuxSmbAsync(string smbPath)
        {
            try
            {
                if (!_mountPoints.TryGetValue(smbPath, out var mountPoint))
                {
                    return true; // Already disconnected or never connected
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = "umount",
                    Arguments = $"\"{mountPoint}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(startInfo);
                if (process == null) return false;

                await process.WaitForExitAsync();
                
                // Clean up the mount point directory
                try 
                { 
                    Directory.Delete(mountPoint); 
                    _mountPoints.Remove(smbPath);
                } 
                catch { }

                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        private readonly Dictionary<string, string> _mountPoints = new();
    }
}
