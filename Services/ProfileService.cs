using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseBackupApp.Models;

namespace DatabaseBackupApp.Services
{
    public class ProfileService
    {
        private readonly string _profilesDirectory;
        private readonly string _passwordsDirectory;

        public ProfileService()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var appDirectory = Path.Combine(appDataPath, "DatabaseBackupApp");
            
            _profilesDirectory = Path.Combine(appDirectory, "Profiles");
            _passwordsDirectory = Path.Combine(appDirectory, "Passwords");
            
            Directory.CreateDirectory(_profilesDirectory);
            Directory.CreateDirectory(_passwordsDirectory);
        }

        public async Task<List<Profile>> GetAllProfilesAsync()
        {
            var profiles = new List<Profile>();
            
            try
            {
                var profileFiles = Directory.GetFiles(_profilesDirectory, "*.json");
                
                foreach (var file in profileFiles)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var profile = JsonSerializer.Deserialize<Profile>(json);
                        if (profile != null)
                        {
                            profiles.Add(profile);
                        }
                    }
                    catch
                    {
                        // Skip corrupted profile files
                        continue;
                    }
                }
            }
            catch
            {
                // Return empty list if directory doesn't exist or other errors
            }
            
            return profiles.OrderBy(p => p.Name).ToList();
        }

        public async Task<Profile?> LoadProfileAsync(string profileName)
        {
            try
            {
                var fileName = GetSafeFileName(profileName) + ".json";
                var filePath = Path.Combine(_profilesDirectory, fileName);
                
                if (!File.Exists(filePath))
                    return null;
                
                var json = await File.ReadAllTextAsync(filePath);
                var profile = JsonSerializer.Deserialize<Profile>(json);
                
                if (profile != null)
                {
                    // Load encrypted passwords separately
                    await LoadPasswordsAsync(profile);
                }
                
                return profile;
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> SaveProfileAsync(Profile profile, DatabaseConnection sourceConnection, 
            DatabaseConnection targetConnection, BackupSettings backupSettings)
        {
            try
            {
                // Update profile with current settings (excluding passwords)
                profile.SourceConnection.CopyFrom(sourceConnection);
                profile.TargetConnection.CopyFrom(targetConnection);
                profile.BackupSettings.CopyFrom(backupSettings);
                
                // Save profile (without passwords)
                var fileName = GetSafeFileName(profile.Name) + ".json";
                var filePath = Path.Combine(_profilesDirectory, fileName);
                
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };
                
                var json = JsonSerializer.Serialize(profile, options);
                await File.WriteAllTextAsync(filePath, json);
                
                // Save encrypted passwords separately
                await SavePasswordsAsync(profile.Name, sourceConnection.Password, 
                    targetConnection.Password, backupSettings.SmbPassword);
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteProfileAsync(string profileName)
        {
            try
            {
                var fileName = GetSafeFileName(profileName) + ".json";
                var filePath = Path.Combine(_profilesDirectory, fileName);
                
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
                
                // Also delete associated password file
                await DeletePasswordsAsync(profileName);
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ProfileExistsAsync(string profileName)
        {
            var fileName = GetSafeFileName(profileName) + ".json";
            var filePath = Path.Combine(_profilesDirectory, fileName);
            return File.Exists(filePath);
        }

        private async Task SavePasswordsAsync(string profileName, string sourcePassword, 
            string targetPassword, string smbPassword)
        {
            try
            {
                var passwords = new Dictionary<string, string>
                {
                    ["source"] = sourcePassword ?? string.Empty,
                    ["target"] = targetPassword ?? string.Empty,
                    ["smb"] = smbPassword ?? string.Empty
                };
                
                var json = JsonSerializer.Serialize(passwords);
                var encryptedData = EncryptString(json);
                
                var fileName = GetSafeFileName(profileName) + ".pwd";
                var filePath = Path.Combine(_passwordsDirectory, fileName);
                
                await File.WriteAllBytesAsync(filePath, encryptedData);
            }
            catch
            {
                // Ignore password save errors - profile will still work without saved passwords
            }
        }

        private async Task LoadPasswordsAsync(Profile profile)
        {
            try
            {
                var fileName = GetSafeFileName(profile.Name) + ".pwd";
                var filePath = Path.Combine(_passwordsDirectory, fileName);
                
                if (!File.Exists(filePath))
                    return;
                
                var encryptedData = await File.ReadAllBytesAsync(filePath);
                var json = DecryptString(encryptedData);
                var passwords = JsonSerializer.Deserialize<Dictionary<string, string>>(json);
                
                if (passwords != null)
                {
                    profile.SourceConnection.Password = passwords.GetValueOrDefault("source", string.Empty);
                    profile.TargetConnection.Password = passwords.GetValueOrDefault("target", string.Empty);
                    profile.BackupSettings.SmbPassword = passwords.GetValueOrDefault("smb", string.Empty);
                }
            }
            catch
            {
                // Ignore password load errors - profile will still work without saved passwords
            }
        }

        private async Task DeletePasswordsAsync(string profileName)
        {
            try
            {
                var fileName = GetSafeFileName(profileName) + ".pwd";
                var filePath = Path.Combine(_passwordsDirectory, fileName);
                
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch
            {
                // Ignore deletion errors
            }
        }

        private static string GetSafeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            var safeName = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
            return string.IsNullOrWhiteSpace(safeName) ? "profile" : safeName;
        }

        private static byte[] EncryptString(string plainText)
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                return ProtectedData.Protect(Encoding.UTF8.GetBytes(plainText), null, DataProtectionScope.CurrentUser);
            }
            else
            {
                // For non-Windows platforms, use AES encryption with a machine-specific key
                return EncryptStringCrossPlatform(plainText);
            }
        }

        private static string DecryptString(byte[] encryptedData)
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                var decryptedBytes = ProtectedData.Unprotect(encryptedData, null, DataProtectionScope.CurrentUser);
                return Encoding.UTF8.GetString(decryptedBytes);
            }
            else
            {
                // For non-Windows platforms, use AES decryption
                return DecryptStringCrossPlatform(encryptedData);
            }
        }

        private static byte[] EncryptStringCrossPlatform(string plainText)
        {
            // Simple AES encryption for cross-platform compatibility
            // In production, consider using more sophisticated key derivation
            var key = GetMachineKey();
            
            using var aes = Aes.Create();
            aes.Key = key;
            aes.GenerateIV();
            
            using var encryptor = aes.CreateEncryptor();
            var plainBytes = Encoding.UTF8.GetBytes(plainText);
            var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
            
            // Prepend IV to encrypted data
            var result = new byte[aes.IV.Length + encryptedBytes.Length];
            Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
            Array.Copy(encryptedBytes, 0, result, aes.IV.Length, encryptedBytes.Length);
            
            return result;
        }

        private static string DecryptStringCrossPlatform(byte[] encryptedData)
        {
            var key = GetMachineKey();
            
            using var aes = Aes.Create();
            aes.Key = key;
            
            // Extract IV from the beginning of encrypted data
            var iv = new byte[aes.IV.Length];
            var encrypted = new byte[encryptedData.Length - iv.Length];
            
            Array.Copy(encryptedData, 0, iv, 0, iv.Length);
            Array.Copy(encryptedData, iv.Length, encrypted, 0, encrypted.Length);
            
            aes.IV = iv;
            
            using var decryptor = aes.CreateDecryptor();
            var decryptedBytes = decryptor.TransformFinalBlock(encrypted, 0, encrypted.Length);
            
            return Encoding.UTF8.GetString(decryptedBytes);
        }

        private static byte[] GetMachineKey()
        {
            // Generate a machine-specific key based on machine name and user
            var machineInfo = Environment.MachineName + Environment.UserName;
            using var sha256 = SHA256.Create();
            return sha256.ComputeHash(Encoding.UTF8.GetBytes(machineInfo));
        }
    }
}
