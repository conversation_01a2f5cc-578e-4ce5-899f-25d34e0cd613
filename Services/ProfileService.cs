using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseBackupApp.Models;

namespace DatabaseBackupApp.Services
{
    public class ProfileService
    {
        private readonly string _profilesPath;

        public ProfileService()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var appFolder = Path.Combine(appDataPath, "DatabaseBackupApp");
            Directory.CreateDirectory(appFolder);
            _profilesPath = Path.Combine(appFolder, "profiles.json");
        }

        public async Task<List<DatabaseProfile>> LoadProfilesAsync()
        {
            if (!File.Exists(_profilesPath))
                return new List<DatabaseProfile>();

            try
            {
                var json = await File.ReadAllTextAsync(_profilesPath);
                return JsonSerializer.Deserialize<List<DatabaseProfile>>(json) ?? new List<DatabaseProfile>();
            }
            catch
            {
                return new List<DatabaseProfile>();
            }
        }

        public async Task SaveProfilesAsync(List<DatabaseProfile> profiles)
        {
            try
            {
                var json = JsonSerializer.Serialize(profiles, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_profilesPath, json);
            }
            catch
            {
                // Handle save error silently
            }
        }

        public DatabaseConnection ProfileToConnection(DatabaseProfile profile)
        {
            return new DatabaseConnection
            {
                ProfileName = profile.Name,
                Server = profile.Server,
                Database = profile.Database,
                Username = profile.Username,
                Port = profile.Port,
                DatabaseType = profile.DatabaseType,
                UseWindowsAuth = profile.UseWindowsAuth
                // Password is NOT loaded from profile for security
            };
        }

        public DatabaseProfile ConnectionToProfile(DatabaseConnection connection)
        {
            return new DatabaseProfile
            {
                Name = connection.ProfileName,
                Server = connection.Server,
                Database = connection.Database,
                Username = connection.Username,
                Port = connection.Port,
                DatabaseType = connection.DatabaseType,
                UseWindowsAuth = connection.UseWindowsAuth
                // Password is NOT saved to profile for security
            };
        }
    }

    public class DatabaseProfile
    {
        public string Name { get; set; } = string.Empty;
        public string Server { get; set; } = string.Empty;
        public string Database { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public int Port { get; set; }
        public DatabaseType DatabaseType { get; set; }
        public bool UseWindowsAuth { get; set; }
        // Note: Password is intentionally NOT included for security
    }
}