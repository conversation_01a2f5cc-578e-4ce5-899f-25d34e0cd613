using System;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using MySql.Data.MySqlClient;
using Npgsql;
using DatabaseBackupApp.Models;

namespace DatabaseBackupApp.Services
{
    public class DatabaseService
    {
        private readonly SmbService _smbService;

        public DatabaseService()
        {
            _smbService = new SmbService();
        }
        public async Task<bool> TestConnectionAsync(DatabaseConnection connection)
        {
            try
            {
                using var dbConnection = CreateConnection(connection);

                // Cast to specific connection type to use OpenAsync
                switch (connection.DatabaseType)
                {
                    case DatabaseType.SqlServer:
                        await ((SqlConnection)dbConnection).OpenAsync();
                        break;
                    case DatabaseType.MySQL:
                        await ((MySqlConnection)dbConnection).OpenAsync();
                        break;
                    case DatabaseType.PostgreSQL:
                        await ((NpgsqlConnection)dbConnection).OpenAsync();
                        break;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> BackupDatabaseAsync(DatabaseConnection connection, BackupSettings settings)
        {
            // Validate settings
            if (!settings.IsValid)
            {
                throw new ArgumentException("Invalid backup settings. Please check the destination path.");
            }

            // Ensure local directory exists if using local destination
            if (settings.Destination == BackupDestination.Local)
            {
                settings.EnsureLocalDirectoryExists();
            }

            var fileName = settings.GenerateBackupFileName(connection.Database);

            string backupPath;

            if (settings.Destination == BackupDestination.Local)
            {
                backupPath = Path.Combine(settings.LocalPath, fileName);
            }
            else
            {
                // For SMB, first create backup locally, then copy to SMB share
                var tempPath = Path.Combine(Path.GetTempPath(), fileName);
                backupPath = tempPath;
            }

            string finalPath;
            switch (connection.DatabaseType)
            {
                case DatabaseType.SqlServer:
                    finalPath = await BackupSqlServerAsync(connection, backupPath);
                    break;
                case DatabaseType.MySQL:
                    finalPath = await BackupMySqlAsync(connection, backupPath);
                    break;
                case DatabaseType.PostgreSQL:
                    finalPath = await BackupPostgreSqlAsync(connection, backupPath);
                    break;
                default:
                    throw new NotSupportedException($"Database type {connection.DatabaseType} is not supported");
            }

            // If SMB destination, copy the file to SMB share
            if (settings.Destination == BackupDestination.SMB)
            {
                var smbPath = await _smbService.CopyFileToSmbAsync(finalPath, settings.SmbPath, settings.SmbUsername, settings.SmbPassword);

                // Clean up temporary local file
                if (File.Exists(finalPath))
                {
                    File.Delete(finalPath);
                }

                return smbPath;
            }

            return finalPath;
        }

        public async Task<bool> RestoreDatabaseAsync(DatabaseConnection connection, string backupFilePath, BackupSettings? smbSettings = null)
        {
            string localBackupPath = backupFilePath;

            // If the backup file is on SMB share, copy it locally first
            if (smbSettings != null && smbSettings.Destination == BackupDestination.SMB)
            {
                var tempPath = Path.GetTempPath();
                localBackupPath = await _smbService.CopyFileFromSmbAsync(backupFilePath, tempPath, smbSettings.SmbUsername, smbSettings.SmbPassword);
            }

            bool result;
            switch (connection.DatabaseType)
            {
                case DatabaseType.SqlServer:
                    result = await RestoreSqlServerAsync(connection, localBackupPath);
                    break;
                case DatabaseType.MySQL:
                    result = await RestoreMySqlAsync(connection, localBackupPath);
                    break;
                case DatabaseType.PostgreSQL:
                    result = await RestorePostgreSqlAsync(connection, localBackupPath);
                    break;
                default:
                    throw new NotSupportedException($"Database type {connection.DatabaseType} is not supported");
            }

            // Clean up temporary file if it was copied from SMB
            if (smbSettings != null && smbSettings.Destination == BackupDestination.SMB && localBackupPath != backupFilePath)
            {
                try
                {
                    if (File.Exists(localBackupPath))
                    {
                        File.Delete(localBackupPath);
                    }
                }
                catch { } // Ignore cleanup errors
            }

            return result;
        }

        public async Task<bool> SyncDatabasesAsync(DatabaseConnection sourceConnection, DatabaseConnection targetConnection, BackupSettings tempSettings)
        {
            try
            {
                // Create temporary backup
                var backupPath = await BackupDatabaseAsync(sourceConnection, tempSettings);
                
                // Restore to target
                var result = await RestoreDatabaseAsync(targetConnection, backupPath);
                
                // Clean up temporary backup
                if (File.Exists(backupPath))
                {
                    File.Delete(backupPath);
                }
                
                return result;
            }
            catch
            {
                return false;
            }
        }

        private IDbConnection CreateConnection(DatabaseConnection connection)
        {
            return connection.DatabaseType switch
            {
                DatabaseType.SqlServer => new SqlConnection(connection.GetConnectionString()),
                DatabaseType.MySQL => new MySqlConnection(connection.GetConnectionString()),
                DatabaseType.PostgreSQL => new NpgsqlConnection(connection.GetConnectionString()),
                _ => throw new NotSupportedException($"Database type {connection.DatabaseType} is not supported")
            };
        }

        private async Task<string> BackupSqlServerAsync(DatabaseConnection connection, string backupPath)
        {
            var fullPath = backupPath + ".bak";
            var sql = $"BACKUP DATABASE [{connection.Database}] TO DISK = '{fullPath}'";
            
            using var sqlConnection = new SqlConnection(connection.GetConnectionString());
            await sqlConnection.OpenAsync();
            using var command = new SqlCommand(sql, sqlConnection);
            command.CommandTimeout = 300; // 5 minutes timeout
            await command.ExecuteNonQueryAsync();
            
            return fullPath;
        }

        private async Task<string> BackupMySqlAsync(DatabaseConnection connection, string backupPath)
        {
            var fullPath = backupPath + ".sql";
            
            var startInfo = new ProcessStartInfo
            {
                FileName = "mysqldump",
                Arguments = $"-h {connection.Server} -P {connection.Port} -u {connection.Username} -p{connection.Password} {connection.Database}",
                RedirectStandardOutput = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using var process = Process.Start(startInfo);
            if (process == null) throw new InvalidOperationException("Failed to start mysqldump process");

            var output = await process.StandardOutput.ReadToEndAsync();
            await process.WaitForExitAsync();

            if (process.ExitCode != 0)
                throw new InvalidOperationException($"mysqldump failed with exit code {process.ExitCode}");

            await File.WriteAllTextAsync(fullPath, output);
            return fullPath;
        }

        private async Task<string> BackupPostgreSqlAsync(DatabaseConnection connection, string backupPath)
        {
            var fullPath = backupPath + ".sql";
            
            var startInfo = new ProcessStartInfo
            {
                FileName = "pg_dump",
                Arguments = $"-h {connection.Server} -p {connection.Port} -U {connection.Username} -d {connection.Database}",
                RedirectStandardOutput = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };
            
            startInfo.EnvironmentVariables["PGPASSWORD"] = connection.Password;

            using var process = Process.Start(startInfo);
            if (process == null) throw new InvalidOperationException("Failed to start pg_dump process");

            var output = await process.StandardOutput.ReadToEndAsync();
            await process.WaitForExitAsync();

            if (process.ExitCode != 0)
                throw new InvalidOperationException($"pg_dump failed with exit code {process.ExitCode}");

            await File.WriteAllTextAsync(fullPath, output);
            return fullPath;
        }

        private async Task<bool> RestoreSqlServerAsync(DatabaseConnection connection, string backupFilePath)
        {
            try
            {
                var sql = $"RESTORE DATABASE [{connection.Database}] FROM DISK = '{backupFilePath}' WITH REPLACE";
                
                using var sqlConnection = new SqlConnection(connection.GetConnectionString());
                await sqlConnection.OpenAsync();
                using var command = new SqlCommand(sql, sqlConnection);
                command.CommandTimeout = 300; // 5 minutes timeout
                await command.ExecuteNonQueryAsync();
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> RestoreMySqlAsync(DatabaseConnection connection, string backupFilePath)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "mysql",
                    Arguments = $"-h {connection.Server} -P {connection.Port} -u {connection.Username} -p{connection.Password} {connection.Database}",
                    RedirectStandardInput = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process == null) return false;

                var sqlContent = await File.ReadAllTextAsync(backupFilePath);
                await process.StandardInput.WriteAsync(sqlContent);
                process.StandardInput.Close();
                
                await process.WaitForExitAsync();
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> RestorePostgreSqlAsync(DatabaseConnection connection, string backupFilePath)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "psql",
                    Arguments = $"-h {connection.Server} -p {connection.Port} -U {connection.Username} -d {connection.Database} -f {backupFilePath}",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };
                
                startInfo.EnvironmentVariables["PGPASSWORD"] = connection.Password;

                using var process = Process.Start(startInfo);
                if (process == null) return false;

                await process.WaitForExitAsync();
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
