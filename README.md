# Database Backup & Restore Tool

A cross-platform desktop application built with Avalonia UI for backing up and restoring databases across multiple database systems.

## Features

### Supported Database Systems
- **Microsoft SQL Server** (MSSQL)
- **MySQL/MariaDB**
- **PostgreSQL**

### Core Functionality
- **Database Backup**: Create backups of your databases
- **Database Restore**: Restore databases from backup files
- **One-Way Sync**: Dump from source database and restore to target database in one operation
- **Cross-Platform**: Runs on Windows, Linux, and macOS

### Backup Destinations
- **Local Storage**: Save backups to local directories
- **SMB/Network Shares**: Save backups to network shares with authentication

## Prerequisites

### .NET Runtime
- .NET 8.0 or later

### Database Tools (for backup/restore operations)
- **SQL Server**: SQL Server Management Studio or sqlcmd
- **MySQL**: mysqldump and mysql client tools
- **PostgreSQL**: pg_dump and psql client tools

## Installation

1. Clone or download the project
2. Ensure .NET 8.0 SDK is installed
3. Build the project:
   ```bash
   dotnet build
   ```
4. Run the application:
   ```bash
   dotnet run
   ```

## Usage

### Database Connection Setup
1. **Source Database**: Configure the database you want to backup from
2. **Target Database**: Configure the database you want to restore to
3. Fill in the connection details:
   - Database Type (SQL Server, MySQL, PostgreSQL)
   - Server address and port
   - Database name
   - Username and password (or Windows Authentication for SQL Server)

### Operations

#### Backup Database
1. Configure source database connection
2. Set backup destination (Local or SMB)
3. For local: Browse and select backup folder
4. For SMB: Enter SMB path and credentials
5. Click "Backup Database"

#### Restore Database
1. Configure target database connection
2. Browse and select backup file
3. Click "Restore" (will prompt for overwrite confirmation)

#### One-Way Sync
1. Configure both source and target database connections
2. Click "One-Way Sync"
3. This will backup the source database and immediately restore it to the target

### SMB/Network Share Support
- Supports Windows network shares (\\\\server\\share)
- Supports Linux CIFS/SMB mounts
- Requires username and password authentication
- Automatically handles mounting/unmounting on Linux

## File Formats

- **SQL Server**: .bak files (native SQL Server backup format)
- **MySQL**: .sql files (SQL dump format)
- **PostgreSQL**: .sql files (SQL dump format)

## Security Notes

- Passwords are stored in memory only during operations
- SMB credentials are used only for the duration of file operations
- No credentials are persisted to disk
- Use Windows Authentication when possible for SQL Server

## Troubleshooting

### Common Issues
1. **Connection Failed**: Verify server address, port, and credentials
2. **Backup Failed**: Ensure sufficient disk space and write permissions
3. **Restore Failed**: Verify backup file format matches database type
4. **SMB Access Denied**: Check network connectivity and SMB credentials

### Database Tool Requirements
- Ensure database client tools are installed and accessible in system PATH
- For MySQL: Install MySQL client tools
- For PostgreSQL: Install PostgreSQL client tools
- For SQL Server: Ensure SQL Server client libraries are available

## Architecture

### Project Structure
```
DatabaseBackupApp/
├── Models/
│   └── DatabaseConnection.cs    # Database connection models
├── Services/
│   ├── DatabaseService.cs       # Core database operations
│   ├── SmbService.cs            # SMB/network share handling
│   └── FileDialogService.cs     # File system dialogs
├── ViewModels/
│   └── MainWindowViewModel.cs   # Main UI logic
├── Views/
│   ├── MainWindow.axaml         # Main UI layout
│   └── MainWindow.axaml.cs      # UI code-behind
├── App.axaml                    # Application resources
├── App.axaml.cs                 # Application startup
└── Program.cs                   # Entry point
```

### Key Components
- **DatabaseService**: Handles all database backup/restore operations
- **SmbService**: Manages SMB/network share connections
- **FileDialogService**: Provides cross-platform file/folder selection
- **MainWindowViewModel**: MVVM pattern implementation for UI binding

## License

This project is provided as-is for educational and development purposes.

## Contributing

Feel free to submit issues and enhancement requests!
